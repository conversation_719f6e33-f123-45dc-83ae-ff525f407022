﻿@{
    ViewData["Title"] = "الصفحة الرئيسية";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<!-- Stats Cards -->
<div class="filament-stats">
    <div class="filament-stat-card">
        <div class="filament-stat-icon primary">
            <i class="fas fa-users"></i>
        </div>
        <div class="filament-stat-value">125</div>
        <div class="filament-stat-label">إجمالي النزلاء</div>
    </div>

    <div class="filament-stat-card">
        <div class="filament-stat-icon success">
            <i class="fas fa-building"></i>
        </div>
        <div class="filament-stat-value">3</div>
        <div class="filament-stat-label">العنابر النشطة</div>
    </div>

    <div class="filament-stat-card">
        <div class="filament-stat-icon warning">
            <i class="fas fa-door-open"></i>
        </div>
        <div class="filament-stat-value">18</div>
        <div class="filament-stat-label">الغرف المتاحة</div>
    </div>

    <div class="filament-stat-card">
        <div class="filament-stat-icon danger">
            <i class="fas fa-gavel"></i>
        </div>
        <div class="filament-stat-value">45</div>
        <div class="filament-stat-label">القضايا النشطة</div>
    </div>
</div>

<!-- Welcome Card -->
<div class="filament-card mb-6">
    <div class="filament-card-header military-header">
        <h2 class="filament-card-title">
            <img src="~/images/logo.svg" alt="شعار القوات المسلحة" style="width: 40px; height: 40px; margin-left: 12px;">
            مرحباً بك في نظام إدارة سجن الكويفية
        </h2>
    </div>
    <div class="filament-card-content">
        <div class="text-center">
            <p style="font-size: 18px; color: var(--gray-700); margin-bottom: 24px;">
                نظام شامل لإدارة السجون والنزلاء - الكتيبة 210 مشاة آلية
            </p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-top: 32px;">
                <a href="@Url.Action("Control", "Prisoners")" class="filament-btn filament-btn-primary filament-btn-lg">
                    <i class="fas fa-list-alt"></i>
                    كنترول النزلاء
                </a>
                <a href="@Url.Action("Index", "Prisoners")" class="filament-btn filament-btn-secondary filament-btn-lg">
                    <i class="fas fa-users"></i>
                    إدارة النزلاء
                </a>
                <a href="@Url.Action("Index", "Wards")" class="filament-btn filament-btn-secondary filament-btn-lg">
                    <i class="fas fa-building"></i>
                    إدارة العنابر
                </a>
                <a href="@Url.Action("Index", "PrisonerCases")" class="filament-btn filament-btn-secondary filament-btn-lg">
                    <i class="fas fa-gavel"></i>
                    إدارة القضايا
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Grid -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; margin-bottom: 32px;">
    <!-- إدارة النزلاء -->
    <div class="filament-card">
        <div class="filament-card-header">
            <h3 class="filament-card-title">
                <i class="fas fa-users"></i>
                إدارة النزلاء
            </h3>
        </div>
        <div class="filament-card-content">
            <div style="display: flex; flex-direction: column; gap: 12px;">
                <a href="@Url.Action("Create", "Prisoners")" class="filament-btn filament-btn-secondary">
                    <i class="fas fa-user-plus"></i>
                    إضافة نزيل جديد
                </a>
                <a href="@Url.Action("Index", "Prisoners")" class="filament-btn filament-btn-secondary">
                    <i class="fas fa-list"></i>
                    قائمة النزلاء
                </a>
                <a href="@Url.Action("Index", "PrisonerMovements")" class="filament-btn filament-btn-secondary">
                    <i class="fas fa-exchange-alt"></i>
                    حركات النزلاء
                </a>
            </div>
        </div>
    </div>

    <!-- إدارة المرافق -->
    <div class="filament-card">
        <div class="filament-card-header">
            <h3 class="filament-card-title">
                <i class="fas fa-building"></i>
                إدارة المرافق
            </h3>
        </div>
        <div class="filament-card-content">
            <div style="display: flex; flex-direction: column; gap: 12px;">
                <a href="@Url.Action("Index", "Wards")" class="filament-btn filament-btn-secondary">
                    <i class="fas fa-building"></i>
                    إدارة العنابر
                </a>
                <a href="@Url.Action("Index", "Rooms")" class="filament-btn filament-btn-secondary">
                    <i class="fas fa-door-open"></i>
                    إدارة الغرف
                </a>
                <a href="@Url.Action("Create", "Wards")" class="filament-btn filament-btn-secondary">
                    <i class="fas fa-plus-circle"></i>
                    إضافة عنبر جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="filament-card">
    <div class="filament-card-header">
        <h3 class="filament-card-title">
            <i class="fas fa-clock"></i>
            الأنشطة الأخيرة
        </h3>
    </div>
    <div class="filament-card-content">
        <div style="display: flex; flex-direction: column; gap: 16px;">
            <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                <div style="width: 40px; height: 40px; background: var(--primary-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user-plus" style="color: var(--primary-600);"></i>
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: var(--gray-900);">تم إضافة نزيل جديد</div>
                    <div style="font-size: 14px; color: var(--gray-600);">أحمد محمد العلي - العنبر الأول</div>
                </div>
                <div style="font-size: 12px; color: var(--gray-500);">منذ ساعتين</div>
            </div>

            <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                <div style="width: 40px; height: 40px; background: var(--success-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-gavel" style="color: var(--success-500);"></i>
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: var(--gray-900);">تم تحديث قضية</div>
                    <div style="font-size: 14px; color: var(--gray-600);">قضية رقم C001 - تم تحديد موعد المحاكمة</div>
                </div>
                <div style="font-size: 12px; color: var(--gray-500);">منذ 4 ساعات</div>
            </div>

            <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                <div style="width: 40px; height: 40px; background: var(--warning-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-exchange-alt" style="color: var(--warning-500);"></i>
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: var(--gray-900);">حركة نقل</div>
                    <div style="font-size: 14px; color: var(--gray-600);">نقل نزيل من العنبر الثاني إلى العنبر الأول</div>
                </div>
                <div style="font-size: 12px; color: var(--gray-500);">أمس</div>
            </div>
        </div>
    </div>
</div>


