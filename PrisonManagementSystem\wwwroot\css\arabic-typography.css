/* Enhanced Arabic Typography for Professional UI */

/* Import Premium Arabic Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* Root Typography Variables */
:root {
    --font-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Tajawal', 'Arial', sans-serif;
    --font-decorative: '<PERSON><PERSON>', 'Times New Roman', serif;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
    
    --letter-spacing-tighter: -0.05em;
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;
}

/* Base Typography */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary) !important;
    font-weight: 400;
    line-height: var(--line-height-normal);
    color: #1a202c;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    direction: rtl;
    text-align: right;
}

/* Enhanced Headings */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: var(--line-height-tight);
    color: #2d3748;
    margin-bottom: 0.5em;
    letter-spacing: var(--letter-spacing-tight);
}

h1, .h1 {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    letter-spacing: var(--letter-spacing-tighter);
}

h2, .h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
}

h3, .h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
}

h4, .h4 {
    font-size: var(--font-size-xl);
    font-weight: 600;
}

h5, .h5 {
    font-size: var(--font-size-lg);
    font-weight: 500;
}

h6, .h6 {
    font-size: var(--font-size-base);
    font-weight: 500;
}

/* Enhanced Paragraphs */
p {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: #4a5568;
    margin-bottom: 1em;
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: 400;
    line-height: var(--line-height-relaxed);
    color: #2d3748;
}

/* Enhanced Text Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

.tracking-tighter { letter-spacing: var(--letter-spacing-tighter); }
.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }
.tracking-widest { letter-spacing: var(--letter-spacing-widest); }

/* Enhanced Form Typography */
label {
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: #2d3748;
    display: block;
    margin-bottom: 0.5rem;
}

input, textarea, select {
    font-family: var(--font-primary) !important;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: #2d3748;
}

input::placeholder,
textarea::placeholder {
    font-family: var(--font-primary);
    color: #a0aec0;
    font-weight: 400;
}

/* Enhanced Button Typography */
button, .btn {
    font-family: var(--font-primary) !important;
    font-weight: 600;
    font-size: var(--font-size-sm);
    letter-spacing: var(--letter-spacing-wide);
    text-transform: none;
}

/* Enhanced Table Typography */
table {
    font-family: var(--font-primary);
}

th {
    font-weight: 700;
    font-size: var(--font-size-sm);
    color: #2d3748;
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wider);
}

td {
    font-size: var(--font-size-sm);
    color: #4a5568;
    font-weight: 500;
}

/* Enhanced Navigation Typography */
.nav-link, .navbar-nav .nav-link {
    font-family: var(--font-primary) !important;
    font-weight: 500;
    font-size: var(--font-size-sm);
    letter-spacing: var(--letter-spacing-wide);
}

/* Enhanced Badge Typography */
.badge {
    font-family: var(--font-primary) !important;
    font-weight: 600;
    font-size: var(--font-size-xs);
    letter-spacing: var(--letter-spacing-wider);
    text-transform: uppercase;
}

/* Enhanced Card Typography */
.card-title {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: #2d3748;
}

.card-subtitle {
    font-family: var(--font-primary);
    font-weight: 500;
    font-size: var(--font-size-sm);
    color: #718096;
}

.card-text {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: #4a5568;
}

/* Enhanced Alert Typography */
.alert {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: var(--line-height-normal);
}

/* Enhanced Breadcrumb Typography */
.breadcrumb {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Enhanced Dropdown Typography */
.dropdown-item {
    font-family: var(--font-primary) !important;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Enhanced Modal Typography */
.modal-title {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: #2d3748;
}

.modal-body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: #4a5568;
}

/* Enhanced Tooltip Typography */
.tooltip {
    font-family: var(--font-primary) !important;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Enhanced Popover Typography */
.popover {
    font-family: var(--font-primary) !important;
}

.popover-header {
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.popover-body {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

/* Responsive Typography */
@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    h1, .h1 { font-size: var(--font-size-3xl); }
    h2, .h2 { font-size: var(--font-size-2xl); }
    h3, .h3 { font-size: var(--font-size-xl); }
    h4, .h4 { font-size: var(--font-size-lg); }
    h5, .h5 { font-size: var(--font-size-base); }
    h6, .h6 { font-size: var(--font-size-sm); }
}

@media (max-width: 480px) {
    html {
        font-size: 13px;
    }
}

/* Print Typography */
@media print {
    body {
        font-family: var(--font-primary) !important;
        font-size: 12pt;
        line-height: 1.4;
        color: #000 !important;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        page-break-after: avoid;
    }
    
    p {
        orphans: 3;
        widows: 3;
    }
}
