#pragma checksum "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "12b82ebb8f13a51a0dd72d6f97b71a411884c67f3f2cc61c90c9e00466bbf959"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Rooms_Index), @"mvc.1.0.view", @"/Views/Rooms/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem

#nullable disable
    ;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"12b82ebb8f13a51a0dd72d6f97b71a411884c67f3f2cc61c90c9e00466bbf959", @"/Views/Rooms/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6b5cda3430a180463c664dbd4c2b7104438aceea37f75cdb0f39bc7283777da", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Rooms_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
       IEnumerable<PrisonManagementSystem.Models.Room>

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("\n");
#nullable restore
#line 3 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
  
    ViewData["Title"] = "إدارة الغرف";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""filament-page"">
    <!-- Header Section -->
    <div class=""filament-header"">
        <div class=""filament-header-content"">
            <div class=""filament-header-heading"">
                <h1 class=""filament-header-title"">
                    <svg class=""filament-header-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z""></path>
                    </svg>
                    إدارة الغرف
                </h1>
                <p class=""filament-header-subtitle"">إدارة ومتابعة جميع الغرف في العنابر</p>
            </div>
            <div class=""filament-header-actions"">
                <a");
            BeginWriteAttribute("href", " href=\"", 1064, "\"", 1092, 1);
            WriteAttributeValue("", 1071, 
#nullable restore
#line 22 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                          Url.Action("Create")

#line default
#line hidden
#nullable disable
            , 1071, 21, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-primary"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 6v6m0 0v6m0-6h6m-6 0H6""></path>
                    </svg>
                    إضافة غرفة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class=""filament-stats-grid"">
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-blue"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2");
            WriteLiteral("z\"></path>\n                    </svg>\n                </div>\n                <div class=\"filament-stats-details\">\n                    <div class=\"filament-stats-value\">");
            Write(
#nullable restore
#line 42 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                       Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">إجمالي الغرف</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-green"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 56 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                       Model.Count(r => r.IsActive)

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">غرف نشطة</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-yellow"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 70 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                       Model.Sum(r => r.CurrentCount)

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">إجمالي النزلاء</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-purple"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 84 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                       Model.Sum(r => r.MaxCapacity)

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">السعة الإجمالية</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class=""filament-card"">
        <div class=""filament-card-header"">
            <div class=""filament-card-header-content"">
                <h3 class=""filament-card-title"">قائمة الغرف</h3>
                <p class=""filament-card-description"">إدارة وتنظيم الغرف في العنابر</p>
            </div>
            <div class=""filament-card-header-actions"">
                <div class=""filament-search-input"">
                    <svg class=""filament-search-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z""></path>
                    </svg>
                    <input type=""text"" placeholder=""البحث في الغرف..."" class=""filament-search-field"" id=""searchInput"">
                </div>
          ");
            WriteLiteral("  </div>\n        </div>\n\n");
#nullable restore
#line 108 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
         if (Model.Any())
        {

#line default
#line hidden
#nullable disable

            WriteLiteral("            <!-- Grid View -->\n            <div class=\"filament-grid-container\">\n");
#nullable restore
#line 112 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                 foreach (var room in Model)
                {

#line default
#line hidden
#nullable disable

            WriteLiteral("                    <div class=\"filament-room-card\">\n                        <div class=\"filament-room-card-header\">\n                            <div class=\"filament-room-card-title\">\n                                <h4>");
            Write(
#nullable restore
#line 117 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                     room.RoomNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</h4>\n");
#nullable restore
#line 118 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                 if (room.IsActive)
                                {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                    <span class=\"filament-badge filament-badge-success\">نشطة</span>\n");
#nullable restore
#line 121 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                }
                                else
                                {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                    <span class=\"filament-badge filament-badge-danger\">غير نشطة</span>\n");
#nullable restore
#line 125 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                }

#line default
#line hidden
#nullable disable

            WriteLiteral("                            </div>\n                            <div class=\"filament-room-card-actions\">\n                                <a");
            BeginWriteAttribute("href", " href=\"", 6900, "\"", 6951, 1);
            WriteAttributeValue("", 6907, 
#nullable restore
#line 128 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                          Url.Action("Details", new { id = room.Id })

#line default
#line hidden
#nullable disable
            , 6907, 44, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-room-action"" title=""عرض التفاصيل"">
                                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M15 12a3 3 0 11-6 0 3 3 0 016 0z""></path>
                                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z""></path>
                                    </svg>
                                </a>
                                <a");
            BeginWriteAttribute("href", " href=\"", 7611, "\"", 7659, 1);
            WriteAttributeValue("", 7618, 
#nullable restore
#line 134 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                          Url.Action("Edit", new { id = room.Id })

#line default
#line hidden
#nullable disable
            , 7618, 41, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-room-action"" title=""تعديل"">
                                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z""></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        
                        <div class=""filament-room-card-content"">
                            <div class=""filament-room-ward"">
                                <span class=""filament-room-ward-label"">العنبر:</span>
                                <span class=""filament-room-ward-name"">");
            Write(
#nullable restore
#line 145 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                                        room.Ward?.Name ?? "غير محدد"

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</span>\n                            </div>\n                            \n                            <p class=\"filament-room-description\">");
            Write(
#nullable restore
#line 148 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                                   room.Description ?? "لا يوجد وصف"

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</p>
                            
                            <div class=""filament-room-stats"">
                                <div class=""filament-room-stat"">
                                    <span class=""filament-room-stat-label"">العدد الحالي</span>
                                    <span class=""filament-room-stat-value"">");
            Write(
#nullable restore
#line 153 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                                            room.CurrentCount

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</span>
                                </div>
                                <div class=""filament-room-stat"">
                                    <span class=""filament-room-stat-label"">السعة القصوى</span>
                                    <span class=""filament-room-stat-value"">");
            Write(
#nullable restore
#line 157 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                                            room.MaxCapacity

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</span>\n                                </div>\n                            </div>\n                            \n                            <div class=\"filament-room-progress\">\n                                <div class=\"filament-room-progress-bar\">\n");
#nullable restore
#line 163 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                      
                                        var occupancyPercentage = room.MaxCapacity > 0 ? (room.CurrentCount * 100.0 / room.MaxCapacity) : 0;
                                        var progressClass = occupancyPercentage > 80 ? "filament-progress-danger" : 
                                                          occupancyPercentage > 60 ? "filament-progress-warning" : "filament-progress-success";
                                    

#line default
#line hidden
#nullable disable

            WriteLiteral("                                    <div");
            BeginWriteAttribute("class", " class=\"", 10116, "\"", 10166, 2);
            WriteAttributeValue("", 10124, "filament-room-progress-fill", 10124, 27, true);
            WriteAttributeValue(" ", 10151, 
#nullable restore
#line 168 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                                             progressClass

#line default
#line hidden
#nullable disable
            , 10152, 14, false);
            EndWriteAttribute();
            BeginWriteAttribute("style", " style=\"", 10167, "\"", 10203, 3);
            WriteAttributeValue("", 10175, "width:", 10175, 6, true);
            WriteAttributeValue(" ", 10181, 
#nullable restore
#line 168 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                                                                           occupancyPercentage

#line default
#line hidden
#nullable disable
            , 10182, 20, false);
            WriteAttributeValue("", 10202, "%", 10202, 1, true);
            EndWriteAttribute();
            WriteLiteral("></div>\n                                </div>\n                                <span class=\"filament-room-progress-text\">");
            Write(
#nullable restore
#line 170 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                                                                           occupancyPercentage.ToString("F1")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("% ممتلئة</span>\n                            </div>\n                        </div>\n                    </div>\n");
#nullable restore
#line 174 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                }

#line default
#line hidden
#nullable disable

            WriteLiteral("            </div>\n");
#nullable restore
#line 176 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
        }
        else
        {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"            <div class=""filament-empty-state"">
                <div class=""filament-empty-state-icon"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z""></path>
                    </svg>
                </div>
                <h3 class=""filament-empty-state-title"">لا توجد غرف مسجلة</h3>
                <p class=""filament-empty-state-description"">ابدأ بإضافة أول غرفة في النظام</p>
                <a");
            BeginWriteAttribute("href", " href=\"", 11266, "\"", 11294, 1);
            WriteAttributeValue("", 11273, 
#nullable restore
#line 187 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
                          Url.Action("Create")

#line default
#line hidden
#nullable disable
            , 11273, 21, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-primary"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 6v6m0 0v6m0-6h6m-6 0H6""></path>
                    </svg>
                    إضافة غرفة جديدة
                </a>
            </div>
");
#nullable restore
#line 194 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Index.cshtml"
        }

#line default
#line hidden
#nullable disable

            WriteLiteral("    </div>\n</div>\n\n");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        // البحث في الغرف
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const roomCards = document.querySelectorAll('.filament-room-card');
            
            roomCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                card.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // تأثيرات تفاعلية
        document.querySelectorAll('.filament-room-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            });
        });");
                WriteLiteral("\n    </script>\n");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<IEnumerable<PrisonManagementSystem.Models.Room>> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
