#pragma checksum "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "161b8aac2f3bd866a9c5870e69879678cbdaad89b7d6c75b04652411dec630d3"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Prisoners_Index), @"mvc.1.0.view", @"/Views/Prisoners/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem

#nullable disable
    ;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"161b8aac2f3bd866a9c5870e69879678cbdaad89b7d6c75b04652411dec630d3", @"/Views/Prisoners/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6b5cda3430a180463c664dbd4c2b7104438aceea37f75cdb0f39bc7283777da", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Prisoners_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
       IEnumerable<PrisonManagementSystem.Models.Prisoner>

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("\n");
#nullable restore
#line 3 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
  
    ViewData["Title"] = "قائمة النزلاء";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""filament-page filament-typography-enhanced"">
    <!-- Header Section -->
    <div class=""filament-header filament-header-enhanced"">
        <div class=""filament-header-content"">
            <div class=""filament-header-heading"">
                <h1 class=""filament-title-enhanced"">
                    <svg class=""filament-header-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z""></path>
                    </svg>
                    قائمة النزلاء
                </h1>
                <p class=""filament-subtitle-enhanced"">إدارة ومتابعة شاملة لجميع النزلاء في النظام مع أحدث التقنيات</p>
            </div>
            <div class=""filamen");
            WriteLiteral("t-header-actions\">\n                <a");
            BeginWriteAttribute("href", " href=\"", 1221, "\"", 1249, 1);
            WriteAttributeValue("", 1228, 
#nullable restore
#line 22 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                          Url.Action("Create")

#line default
#line hidden
#nullable disable
            , 1228, 21, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-primary filament-button-enhanced"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 6v6m0 0v6m0-6h6m-6 0H6""></path>
                    </svg>
                    إضافة نزيل جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class=""filament-stats-enhanced"">
        <div class=""filament-stat-card-enhanced blue"">
            <div class=""filament-stat-content-enhanced"">
                <div class=""filament-stat-icon-enhanced"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 01");
            WriteLiteral(@"9.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stat-value-enhanced"">");
            Write(
#nullable restore
#line 42 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                               Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stat-label-enhanced"">إجمالي النزلاء</div>
                </div>
            </div>
        </div>

        <div class=""filament-stat-card-enhanced green"">
            <div class=""filament-stat-content-enhanced"">
                <div class=""filament-stat-icon-enhanced"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stat-value-enhanced"">");
            Write(
#nullable restore
#line 56 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                               Model.Count(p => p.Status == "محبوس")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stat-label-enhanced"">محبوسين حالياً</div>
                </div>
            </div>
        </div>

        <div class=""filament-stat-card-enhanced yellow"">
            <div class=""filament-stat-content-enhanced"">
                <div class=""filament-stat-icon-enhanced"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stat-value-enhanced"">");
            Write(
#nullable restore
#line 70 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                               Model.Count(p => p.Status == "مستحق الإفراج")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stat-label-enhanced"">مستحقين الإفراج</div>
                </div>
            </div>
        </div>

        <div class=""filament-stat-card-enhanced red"">
            <div class=""filament-stat-content-enhanced"">
                <div class=""filament-stat-icon-enhanced"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stat-value-enhanced"">");
            Write(
#nullable restore
#line 84 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                               Model.Count(p => p.HasPreviousImprisonment)

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stat-label-enhanced"">سوابق سجن</div>
                </div>
            </div>
        </div>
    </div>
    <!-- Main Content -->
    <div class=""filament-card"">
        <div class=""filament-card-header"">
            <div class=""filament-card-header-content"">
                <h3 class=""filament-card-title"">جدول النزلاء</h3>
                <p class=""filament-card-description"">قائمة شاملة بجميع النزلاء المسجلين في النظام</p>
            </div>
            <div class=""filament-card-header-actions"">
                <div class=""filament-search-input"">
                    <svg class=""filament-search-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z""></path>
                    </svg>
                    <input type=""text"" placeholder=""البحث في النزلاء..."" class=""filament-search-field"" id=""searchInput"">
              ");
            WriteLiteral("  </div>\n            </div>\n        </div>\n\n");
#nullable restore
#line 107 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
         if (Model.Any())
        {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"            <div class=""filament-table-container"">
                <table class=""filament-table filament-table-enhanced"">
                    <thead class=""filament-table-header"">
                        <tr>
                            <th class=""filament-table-header-cell"">رقم السجين</th>
                            <th class=""filament-table-header-cell"">الاسم الكامل</th>
                            <th class=""filament-table-header-cell"">العمر</th>
                            <th class=""filament-table-header-cell"">الجنسية</th>
                            <th class=""filament-table-header-cell"">العنبر</th>
                            <th class=""filament-table-header-cell"">الغرفة</th>
                            <th class=""filament-table-header-cell"">تاريخ الدخول</th>
                            <th class=""filament-table-header-cell"">الحالة</th>
                            <th class=""filament-table-header-cell"">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody");
            WriteLiteral(" class=\"filament-table-body\">\n");
#nullable restore
#line 125 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                         foreach (var item in Model)
                        {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                            <tr class=""filament-table-row"">
                                <td class=""filament-table-cell"">
                                    <div class=""filament-table-cell-content"">
                                        <span class=""filament-badge filament-badge-primary"">");
            Write(
#nullable restore
#line 130 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                                             item.PrisonerNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</span>
                                    </div>
                                </td>
                                <td class=""filament-table-cell"">
                                    <div class=""filament-table-cell-content"">
                                        <div class=""filament-table-cell-primary"">");
            Write(
#nullable restore
#line 135 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                                  item.FullName

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                        <div class=\"filament-table-cell-secondary\">");
            Write(
#nullable restore
#line 136 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                                    item.MotherName

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                    </div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 140 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                              item.Age

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(" سنة</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 143 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                              item.Nationality

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 146 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                               item.Ward?.Name ?? "غير محدد"

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 149 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                               item.Room?.RoomNumber ?? "غير محدد"

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 152 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                                              item.EntryDate.ToString("yyyy/MM/dd")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">\n");
#nullable restore
#line 156 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                         if (item.Status == "مستحق الإفراج")
                                        {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                            <span class=\"filament-badge filament-badge-warning\">مستحق الإفراج</span>\n");
#nullable restore
#line 159 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                        }
                                        else
                                        {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                            <span class=\"filament-badge filament-badge-success\">محبوس</span>\n");
#nullable restore
#line 163 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                        }

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                                    </div>
                                </td>
                                <td class=""filament-table-cell"">
                                    <div class=""filament-table-actions"">
                                        <a");
            BeginWriteAttribute("href", " href=\"", 9981, "\"", 10032, 1);
            WriteAttributeValue("", 9988, 
#nullable restore
#line 168 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                  Url.Action("Details", new { id = item.Id })

#line default
#line hidden
#nullable disable
            , 9988, 44, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-table-action filament-table-action-view"" title=""عرض التفاصيل"">
                                            <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M15 12a3 3 0 11-6 0 3 3 0 016 0z""></path>
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z""></path>
                                            </svg>
                                        </a>
                                        <a");
            BeginWriteAttribute("href", " href=\"", 10768, "\"", 10816, 1);
            WriteAttributeValue("", 10775, 
#nullable restore
#line 174 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                  Url.Action("Edit", new { id = item.Id })

#line default
#line hidden
#nullable disable
            , 10775, 41, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-table-action filament-table-action-edit"" title=""تعديل"">
                                            <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z""></path>
                                            </svg>
                                        </a>
                                        <a");
            BeginWriteAttribute("href", " href=\"", 11381, "\"", 11431, 1);
            WriteAttributeValue("", 11388, 
#nullable restore
#line 179 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                                                  Url.Action("Delete", new { id = item.Id })

#line default
#line hidden
#nullable disable
            , 11388, 43, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-table-action filament-table-action-delete"" title=""حذف"" onclick=""return confirm('هل أنت متأكد من حذف هذا النزيل؟')"">
                                            <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16""></path>
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                            </tr>
");
#nullable restore
#line 187 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                        }

#line default
#line hidden
#nullable disable

            WriteLiteral("                    </tbody>\n                </table>\n            </div>\n");
#nullable restore
#line 191 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
        }
        else
        {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"            <div class=""filament-empty-state"">
                <div class=""filament-empty-state-icon"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z""></path>
                    </svg>
                </div>
                <h3 class=""filament-empty-state-title"">لا توجد نزلاء مسجلين</h3>
                <p class=""filament-empty-state-description"">ابدأ بإضافة أول نزيل في النظام</p>
                <a");
            BeginWriteAttribute("href", " href=\"", 13063, "\"", 13091, 1);
            WriteAttributeValue("", 13070, 
#nullable restore
#line 202 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
                          Url.Action("Create")

#line default
#line hidden
#nullable disable
            , 13070, 21, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-primary"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 6v6m0 0v6m0-6h6m-6 0H6""></path>
                    </svg>
                    إضافة نزيل جديد
                </a>
            </div>
");
#nullable restore
#line 209 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Index.cshtml"
        }

#line default
#line hidden
#nullable disable

            WriteLiteral("    </div>\n</div>\n\n");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        // البحث في الجدول
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('.filament-table-row');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // تأثيرات تفاعلية
        document.querySelectorAll('.filament-table-row').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<IEnumerable<PrisonManagementSystem.Models.Prisoner>> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
