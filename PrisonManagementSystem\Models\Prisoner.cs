using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PrisonManagementSystem.Models
{
    public class Prisoner
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "رقم السجين")]
        public string PrisonerNumber { get; set; }

        // البيانات الشخصية
        [Required]
        [StringLength(100)]
        [Display(Name = "الاسم الرباعي")]
        public string FullName { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم الأم")]
        public string MotherName { get; set; }

        [Required]
        [Display(Name = "تاريخ الميلاد")]
        public DateTime DateOfBirth { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "مكان الميلاد")]
        public string PlaceOfBirth { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "الجنسية")]
        public string Nationality { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم جواز السفر")]
        public string PassportNumber { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم البطاقة")]
        public string IdCardNumber { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "الرقم الوطني")]
        public string NationalNumber { get; set; }

        // معلومات الاتصال
        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string PhoneNumber { get; set; }

        [StringLength(100)]
        [Display(Name = "أقرب الأقارب")]
        public string ClosestRelativeName { get; set; }

        [StringLength(50)]
        [Display(Name = "القرابة")]
        public string RelationshipType { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم هاتف أقرب الأقارب")]
        public string ClosestRelativePhone { get; set; }

        [StringLength(200)]
        [Display(Name = "عنوان أقرب الأقارب")]
        public string ClosestRelativeAddress { get; set; }

        // الصورة الشخصية
        [StringLength(255)]
        [Display(Name = "مسار الصورة")]
        public string PhotoPath { get; set; }

        // البيانات الصحية
        [StringLength(200)]
        [Display(Name = "الحالة الصحية")]
        public string HealthStatus { get; set; }

        [StringLength(500)]
        [Display(Name = "الأمراض المزمنة")]
        public string ChronicDiseases { get; set; }

        [Display(Name = "نسبة العجز")]
        public decimal? DisabilityPercentage { get; set; }

        // بيانات السجن
        [Required]
        [Display(Name = "تاريخ الدخول")]
        public DateTime EntryDate { get; set; }

        [Display(Name = "هل سبق له الدخول للسجن")]
        public bool HasPreviousImprisonment { get; set; }

        [Display(Name = "تاريخ الخروج المتوقع")]
        public DateTime? ExpectedReleaseDate { get; set; }

        [Display(Name = "إجمالي المدة (بالأيام)")]
        public int? TotalSentenceDays { get; set; }

        // العلاقات
        [Display(Name = "العنبر")]
        public int? WardId { get; set; }
        [ForeignKey("WardId")]
        public virtual Ward Ward { get; set; }

        [Display(Name = "الغرفة")]
        public int? RoomId { get; set; }
        [ForeignKey("RoomId")]
        public virtual Room Room { get; set; }

        // القضايا
        public virtual ICollection<PrisonerCase> Cases { get; set; } = new List<PrisonerCase>();

        // الحركات
        public virtual ICollection<PrisonerMovement> Movements { get; set; } = new List<PrisonerMovement>();

        // العقوبات
        public virtual ICollection<PrisonerPunishment> Punishments { get; set; } = new List<PrisonerPunishment>();

        // تواريخ النظام
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedDate { get; set; }

        [StringLength(50)]
        [Display(Name = "المستخدم المنشئ")]
        public string CreatedBy { get; set; }

        [StringLength(50)]
        [Display(Name = "المستخدم المحدث")]
        public string UpdatedBy { get; set; }

        // خصائص محسوبة
        [NotMapped]
        [Display(Name = "العمر")]
        public int Age => DateTime.Now.Year - DateOfBirth.Year;

        [NotMapped]
        [Display(Name = "حالة السجين")]
        public string Status
        {
            get
            {
                if (ExpectedReleaseDate.HasValue && ExpectedReleaseDate.Value <= DateTime.Now)
                    return "مستحق الإفراج";
                return "محبوس";
            }
        }
    }
}
