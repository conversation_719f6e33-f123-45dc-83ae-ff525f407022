// Prisoners Control Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    // Initialize search functionality
    initializeSearch();
    
    // Initialize table sorting
    initializeTableSorting();
    
    // Initialize filters
    initializeFilters();
    
    // Initialize action menus
    initializeActionMenus();
    
    // Update date/time
    updateDateTime();
    setInterval(updateDateTime, 1000);
}

// Search functionality
function initializeSearch() {
    const searchInputs = document.querySelectorAll('#searchName, #searchNational, #searchCase');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', debounce(performSearch, 300));
    });
}

function performSearch() {
    const searchName = document.getElementById('searchName').value.toLowerCase();
    const searchNational = document.getElementById('searchNational').value.toLowerCase();
    const searchCase = document.getElementById('searchCase').value.toLowerCase();
    const wardFilter = document.getElementById('filterWard').value;
    const statusFilter = document.getElementById('filterStatus').value;
    const nationalityFilter = document.getElementById('filterNationality').value;
    
    const tableRows = document.querySelectorAll('#prisonersTableBody tr');
    let visibleCount = 0;
    
    tableRows.forEach(row => {
        const name = row.querySelector('.prisoner-name').textContent.toLowerCase();
        const national = row.querySelector('.national-number').textContent.toLowerCase();
        const ward = row.querySelector('.ward-cell .filament-badge').textContent;
        const status = row.querySelector('.status-cell .filament-badge').textContent;
        const nationality = row.querySelector('.nationality-badge').textContent;
        
        let shouldShow = true;
        
        // Apply text filters
        if (searchName && !name.includes(searchName)) shouldShow = false;
        if (searchNational && !national.includes(searchNational)) shouldShow = false;
        
        // Apply dropdown filters
        if (wardFilter && !ward.includes(wardFilter)) shouldShow = false;
        if (statusFilter && !status.includes(statusFilter)) shouldShow = false;
        if (nationalityFilter && !nationality.includes(nationalityFilter)) shouldShow = false;
        
        if (shouldShow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // Update results count
    document.getElementById('totalResults').textContent = visibleCount;
}

function clearFilters() {
    document.getElementById('searchName').value = '';
    document.getElementById('searchNational').value = '';
    document.getElementById('searchCase').value = '';
    document.getElementById('filterWard').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterNationality').value = '';
    
    // Show all rows
    const tableRows = document.querySelectorAll('#prisonersTableBody tr');
    tableRows.forEach(row => {
        row.style.display = '';
    });
    
    // Reset count
    document.getElementById('totalResults').textContent = tableRows.length;
}

// Table sorting
function initializeTableSorting() {
    const sortableHeaders = document.querySelectorAll('.sortable');
    
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const sortField = this.dataset.sort;
            sortTable(sortField, this);
        });
    });
}

function sortTable(field, headerElement) {
    const table = document.getElementById('prisonersTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Determine sort direction
    const currentDirection = headerElement.dataset.direction || 'asc';
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    
    // Clear all sort indicators
    document.querySelectorAll('.sortable .sort-icon').forEach(icon => {
        icon.className = 'fas fa-sort sort-icon';
    });
    
    // Set new sort indicator
    const sortIcon = headerElement.querySelector('.sort-icon');
    sortIcon.className = `fas fa-sort-${newDirection === 'asc' ? 'up' : 'down'} sort-icon`;
    headerElement.dataset.direction = newDirection;
    
    // Sort rows
    rows.sort((a, b) => {
        let aValue, bValue;
        
        switch(field) {
            case 'name':
                aValue = a.querySelector('.prisoner-name').textContent;
                bValue = b.querySelector('.prisoner-name').textContent;
                break;
            case 'national':
                aValue = a.querySelector('.national-number').textContent;
                bValue = b.querySelector('.national-number').textContent;
                break;
            case 'birth':
                aValue = new Date(a.querySelector('.date-cell .date-text').textContent);
                bValue = new Date(b.querySelector('.date-cell .date-text').textContent);
                break;
            case 'entry':
                aValue = new Date(a.querySelectorAll('.date-cell .date-text')[1].textContent);
                bValue = new Date(b.querySelectorAll('.date-cell .date-text')[1].textContent);
                break;
            default:
                return 0;
        }
        
        if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
        }
        
        if (newDirection === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

// Filters
function initializeFilters() {
    const filterSelects = document.querySelectorAll('#filterWard, #filterStatus, #filterNationality');
    
    filterSelects.forEach(select => {
        select.addEventListener('change', performSearch);
    });
}

// Action menus
function initializeActionMenus() {
    // Close action menus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.table-actions-dropdown')) {
            document.querySelectorAll('.actions-menu').forEach(menu => {
                menu.style.display = 'none';
            });
        }
    });
}

function toggleActionsMenu(button) {
    const menu = button.nextElementSibling;
    const isVisible = menu.style.display === 'block';
    
    // Hide all other menus
    document.querySelectorAll('.actions-menu').forEach(m => {
        m.style.display = 'none';
    });
    
    // Toggle current menu
    menu.style.display = isVisible ? 'none' : 'block';
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function updateDateTime() {
    const now = new Date();
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };
        dateTimeElement.textContent = now.toLocaleDateString('ar-IQ', options);
    }
}

// Export functions
function exportToExcel() {
    // Get visible rows data
    const visibleRows = Array.from(document.querySelectorAll('#prisonersTableBody tr'))
        .filter(row => row.style.display !== 'none');
    
    if (visibleRows.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // Create CSV content
    let csvContent = 'الاسم الكامل,الرقم الوطني,تاريخ الميلاد,الجنسية,العنبر,الغرفة,التهمة,تاريخ الدخول,الحالة\n';
    
    visibleRows.forEach(row => {
        const name = row.querySelector('.prisoner-name').textContent;
        const national = row.querySelector('.national-number').textContent;
        const birth = row.querySelector('.date-cell .date-text').textContent;
        const nationality = row.querySelector('.nationality-badge').textContent;
        const ward = row.querySelector('.ward-cell .filament-badge').textContent;
        const room = row.querySelector('.room-cell .filament-badge').textContent;
        const charge = row.querySelector('.charge-cell .charge-text')?.textContent || 'غير محدد';
        const entry = row.querySelectorAll('.date-cell .date-text')[1].textContent;
        const status = row.querySelector('.status-cell .filament-badge').textContent;
        
        csvContent += `"${name}","${national}","${birth}","${nationality}","${ward}","${room}","${charge}","${entry}","${status}"\n`;
    });
    
    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `prisoners_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
}

function printResults() {
    const printWindow = window.open('', '_blank');
    const tableHTML = document.querySelector('.filament-table-container').outerHTML;
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>كنترول النزلاء</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .filament-badge { padding: 2px 6px; border-radius: 4px; font-size: 12px; }
                .actions-cell { display: none; }
                .table-checkbox { display: none; }
                @media print {
                    .actions-cell, .table-checkbox { display: none !important; }
                }
            </style>
        </head>
        <body>
            <h1>كنترول النزلاء - ${new Date().toLocaleDateString('ar-IQ')}</h1>
            ${tableHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// Prisoner actions
function movePrisoner(prisonerId) {
    // This would open a modal or redirect to move prisoner page
    alert(`نقل النزيل رقم ${prisonerId} - هذه الوظيفة قيد التطوير`);
}

function deletePrisoner(prisonerId) {
    if (confirm('هل أنت متأكد من حذف هذا النزيل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // Send delete request
        fetch(`/Prisoners/Delete/${prisonerId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
            }
        })
        .then(response => {
            if (response.ok) {
                // Remove row from table
                document.querySelector(`tr[data-prisoner-id="${prisonerId}"]`).remove();
                // Update count
                const currentCount = parseInt(document.getElementById('totalResults').textContent);
                document.getElementById('totalResults').textContent = currentCount - 1;
                
                // Show success message
                showNotification('تم حذف النزيل بنجاح', 'success');
            } else {
                showNotification('حدث خطأ أثناء حذف النزيل', 'error');
            }
        })
        .catch(error => {
            showNotification('حدث خطأ في الاتصال', 'error');
        });
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `filament-alert ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
