/* Filament Forms and Tables Enhancement */

/* Form Styling */
.filament-form {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
    padding: 24px;
    margin-bottom: 24px;
}

.filament-form .row {
    margin-bottom: 20px;
}

.filament-form .form-group {
    margin-bottom: 20px;
}

.filament-form label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 6px;
    display: block;
    font-size: 14px;
}

.filament-form .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: white;
}

.filament-form .form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filament-form .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-left: 40px;
    appearance: none;
}

.filament-form .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.filament-form .form-check-input {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-600);
}

.filament-form .form-check-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0;
}

/* Enhanced Table Styling */
.filament-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-bottom: 24px;
}

.filament-table-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.filament-table-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filament-table-actions {
    display: flex;
    gap: 12px;
}

.filament-table-wrapper {
    overflow-x: auto;
}

.filament-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.filament-table th {
    background-color: var(--gray-50);
    padding: 16px 20px;
    text-align: right;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    font-size: 14px;
    white-space: nowrap;
}

.filament-table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-900);
    font-size: 14px;
    vertical-align: middle;
}

.filament-table tbody tr {
    transition: background-color 0.2s ease;
}

.filament-table tbody tr:hover {
    background-color: var(--gray-50);
}

.filament-table tbody tr:last-child td {
    border-bottom: none;
}

/* Action Buttons in Tables */
.filament-table-actions-cell {
    white-space: nowrap;
}

.filament-table-action {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    margin-left: 4px;
}

.filament-table-action.view {
    background-color: var(--primary-50);
    color: var(--primary-700);
    border-color: var(--primary-200);
}

.filament-table-action.view:hover {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

.filament-table-action.edit {
    background-color: #fef3c7;
    color: #92400e;
    border-color: #fde68a;
}

.filament-table-action.edit:hover {
    background-color: #fde68a;
    color: #78350f;
}

.filament-table-action.delete {
    background-color: #fee2e2;
    color: #991b1b;
    border-color: #fecaca;
}

.filament-table-action.delete:hover {
    background-color: #fecaca;
    color: #7f1d1d;
}

/* Search and Filter Bar */
.filament-search-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
    padding: 20px 24px;
    margin-bottom: 24px;
}

.filament-search-row {
    display: flex;
    gap: 16px;
    align-items: end;
    flex-wrap: wrap;
}

.filament-search-field {
    flex: 1;
    min-width: 200px;
}

.filament-search-field label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 6px;
    display: block;
    font-size: 14px;
}

.filament-search-field input,
.filament-search-field select {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.filament-search-field input:focus,
.filament-search-field select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filament-search-actions {
    display: flex;
    gap: 8px;
}

/* Page Header */
.filament-page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--gray-200);
}

.filament-page-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.filament-page-actions {
    display: flex;
    gap: 12px;
}

/* Breadcrumbs */
.filament-breadcrumbs {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 14px;
}

.filament-breadcrumb {
    color: var(--gray-600);
    text-decoration: none;
    transition: color 0.2s ease;
}

.filament-breadcrumb:hover {
    color: var(--primary-600);
}

.filament-breadcrumb.active {
    color: var(--gray-900);
    font-weight: 500;
}

.filament-breadcrumb-separator {
    color: var(--gray-400);
}

/* Alert Messages */
.filament-alert {
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 12px;
}

.filament-alert.success {
    background-color: #dcfce7;
    border-color: #bbf7d0;
    color: #166534;
}

.filament-alert.error {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.filament-alert.warning {
    background-color: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

.filament-alert.info {
    background-color: var(--primary-50);
    border-color: var(--primary-200);
    color: var(--primary-700);
}

/* Form Validation */
.field-validation-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

.input-validation-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.validation-summary-errors {
    background: #fee2e2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.validation-summary-errors ul {
    margin: 0;
    padding-right: 20px;
    color: #991b1b;
}

/* Empty State */
.filament-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-500);
}

.filament-empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--gray-400);
}

.filament-empty-state-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
}

.filament-empty-state-description {
    font-size: 14px;
    color: var(--gray-500);
    margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filament-search-row {
        flex-direction: column;
    }
    
    .filament-search-field {
        min-width: auto;
    }
    
    .filament-page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .filament-page-actions {
        justify-content: center;
    }
    
    .filament-table-wrapper {
        overflow-x: scroll;
    }
    
    .filament-table th,
    .filament-table td {
        padding: 12px 8px;
        font-size: 12px;
    }
}
