/* Enhanced Forms with Professional Layout */

/* Form Grid System */
.filament-form-grid-horizontal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.filament-form-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.filament-form-grid-3 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
}

.filament-form-grid-4 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 1.5rem;
}

/* Enhanced Form Fields */
.filament-form-field {
    position: relative;
    margin-bottom: 1.5rem;
}

.filament-form-field.focused {
    transform: translateY(-2px);
}

.filament-form-field.focused .filament-form-label {
    color: #6366f1;
    font-weight: 600;
}

/* Enhanced Labels */
.filament-form-label {
    display: block;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
    font-size: 0.95rem;
    color: #374151;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.filament-form-label.required::after {
    content: " *";
    color: #ef4444;
    font-weight: 700;
}

/* Enhanced Inputs */
.filament-form-input,
.filament-form-select,
.filament-form-textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-family: 'Cairo', sans-serif !important;
    font-size: 0.95rem;
    font-weight: 500;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filament-form-input:focus,
.filament-form-select:focus,
.filament-form-textarea:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 
        0 0 0 3px rgba(99, 102, 241, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.filament-form-input::placeholder,
.filament-form-textarea::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* Enhanced Select */
.filament-form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 3rem;
    appearance: none;
}

/* Enhanced Textarea */
.filament-form-textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

/* Enhanced Checkbox */
.filament-form-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 0.75rem;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.filament-form-checkbox:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.filament-form-checkbox-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 0.375rem;
    accent-color: #6366f1;
    cursor: pointer;
}

.filament-form-checkbox-label {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    margin: 0;
}

/* Enhanced File Input */
input[type="file"].filament-form-input {
    padding: 0.75rem;
    border: 2px dashed #d1d5db;
    background: #f9fafb;
    cursor: pointer;
}

input[type="file"].filament-form-input:hover {
    border-color: #6366f1;
    background: #f0f9ff;
}

/* Enhanced Error Messages */
.filament-form-error {
    display: block;
    color: #ef4444;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.5rem;
    font-family: 'Cairo', sans-serif !important;
}

/* Enhanced Hints */
.filament-form-hint {
    display: block;
    color: #6b7280;
    font-size: 0.8rem;
    margin-top: 0.5rem;
    font-family: 'Cairo', sans-serif !important;
}

/* Enhanced Sections */
.filament-section {
    background: white;
    border-radius: 1rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.filament-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.filament-section-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
}

.filament-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
}

.filament-section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-family: 'Cairo', sans-serif !important;
    font-size: 1.375rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.filament-section-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: #6366f1;
}

.filament-section-description {
    font-family: 'Cairo', sans-serif !important;
    color: #6b7280;
    font-size: 0.95rem;
    margin: 0;
    font-weight: 500;
}

.filament-section-content {
    padding: 2rem;
}

/* Enhanced Form Actions */
.filament-form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-top: 1px solid #e5e7eb;
    border-radius: 0 0 1rem 1rem;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filament-form-grid-2,
    .filament-form-grid-3,
    .filament-form-grid-4 {
        grid-template-columns: 1fr;
    }
    
    .filament-section-header,
    .filament-section-content {
        padding: 1.5rem;
    }
    
    .filament-form-actions {
        flex-direction: column;
        padding: 1.5rem;
    }
    
    .filament-form-input,
    .filament-form-select,
    .filament-form-textarea {
        padding: 0.875rem 1rem;
    }
}

/* Enhanced Validation States */
.filament-form-field.has-error .filament-form-input,
.filament-form-field.has-error .filament-form-select,
.filament-form-field.has-error .filament-form-textarea {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.filament-form-field.has-success .filament-form-input,
.filament-form-field.has-success .filament-form-select,
.filament-form-field.has-success .filament-form-textarea {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Enhanced Loading States */
.filament-form-loading .filament-form-input,
.filament-form-loading .filament-form-select,
.filament-form-loading .filament-form-textarea {
    opacity: 0.6;
    pointer-events: none;
}

/* Enhanced Focus Ring */
.filament-form-input:focus-visible,
.filament-form-select:focus-visible,
.filament-form-textarea:focus-visible {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .filament-section {
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
        break-inside: avoid;
    }
    
    .filament-form-actions {
        display: none !important;
    }
}
