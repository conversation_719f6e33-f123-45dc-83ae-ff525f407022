using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using PrisonManagementSystem.Data;
using PrisonManagementSystem.Models;
using System.Linq;
using System.Threading.Tasks;

namespace PrisonManagementSystem.Controllers
{
    [Authorize]
    public class ShilasController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ShilasController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Shilas
        public async Task<IActionResult> Index()
        {
            var shilas = await _context.Shilas
                .Include(s => s.Room)
                .ThenInclude(r => r.Ward)
                .Include(s => s.PrisonerPunishments)
                .ThenInclude(pp => pp.Prisoner)
                .OrderBy(s => s.Room.Ward.Name)
                .ThenBy(s => s.Room.RoomNumber)
                .ThenBy(s => s.Number)
                .ToListAsync();
            return View(shilas);
        }

        // GET: Shilas/Create
        public IActionResult Create()
        {
            // فقط الغرف من نوع "شيلة" أو الغرف التي يمكن تحويلها لشيلة
            ViewData["RoomId"] = new SelectList(
                _context.Rooms
                    .Include(r => r.Ward)
                    .Where(r => r.IsActive && (r.RoomType == "شيلة" || r.RoomType == "عادية"))
                    .Select(r => new {
                        Id = r.Id,
                        DisplayName = $"{r.Ward.Name} - غرفة {r.RoomNumber}"
                    }),
                "Id", "DisplayName");
            return View();
        }

        // POST: Shilas/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Number,Name,Description,MaxCapacity,PunishmentType,MaxPunishmentDays,Conditions,IsActive,RoomId")] Shila shila)
        {
            if (ModelState.IsValid)
            {
                // التحقق من عدم تكرار رقم الشيلة في نفس الغرفة
                var existingShila = await _context.Shilas
                    .FirstOrDefaultAsync(s => s.Number == shila.Number && s.RoomId == shila.RoomId);

                if (existingShila != null)
                {
                    ModelState.AddModelError("Number", "رقم الشيلة موجود بالفعل في هذه الغرفة");
                    ViewData["RoomId"] = new SelectList(
                        _context.Rooms
                            .Include(r => r.Ward)
                            .Where(r => r.IsActive && (r.RoomType == "شيلة" || r.RoomType == "عادية"))
                            .Select(r => new {
                                Id = r.Id,
                                DisplayName = $"{r.Ward.Name} - غرفة {r.RoomNumber}"
                            }),
                        "Id", "DisplayName", shila.RoomId);
                    return View(shila);
                }

                // تحديث نوع الغرفة لتصبح شيلة
                var room = await _context.Rooms.FindAsync(shila.RoomId);
                if (room != null)
                {
                    room.RoomType = "شيلة";
                    _context.Update(room);
                }

                shila.CreatedDate = System.DateTime.Now;
                shila.CurrentCount = 0;

                _context.Add(shila);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["RoomId"] = new SelectList(
                _context.Rooms
                    .Include(r => r.Ward)
                    .Where(r => r.IsActive && (r.RoomType == "شيلة" || r.RoomType == "عادية"))
                    .Select(r => new {
                        Id = r.Id,
                        DisplayName = $"{r.Ward.Name} - غرفة {r.RoomNumber}"
                    }),
                "Id", "DisplayName", shila.RoomId);
            return View(shila);
        }

        // GET: Shilas/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var shila = await _context.Shilas.FindAsync(id);
            if (shila == null)
            {
                return NotFound();
            }
            ViewData["RoomId"] = new SelectList(
                _context.Rooms
                    .Include(r => r.Ward)
                    .Where(r => r.IsActive && (r.RoomType == "شيلة" || r.RoomType == "عادية"))
                    .Select(r => new {
                        Id = r.Id,
                        DisplayName = $"{r.Ward.Name} - غرفة {r.RoomNumber}"
                    }),
                "Id", "DisplayName", shila.RoomId);
            return View(shila);
        }

        // POST: Shilas/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Number,Name,Description,MaxCapacity,CurrentCount,PunishmentType,MaxPunishmentDays,Conditions,IsActive,RoomId,CreatedDate")] Shila shila)
        {
            if (id != shila.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(shila);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ShilaExists(shila.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["RoomId"] = new SelectList(
                _context.Rooms
                    .Include(r => r.Ward)
                    .Where(r => r.IsActive && (r.RoomType == "شيلة" || r.RoomType == "عادية"))
                    .Select(r => new {
                        Id = r.Id,
                        DisplayName = $"{r.Ward.Name} - غرفة {r.RoomNumber}"
                    }),
                "Id", "DisplayName", shila.RoomId);
            return View(shila);
        }

        // GET: Shilas/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var shila = await _context.Shilas
                .Include(s => s.Room)
                .ThenInclude(r => r.Ward)
                .Include(s => s.PrisonerPunishments)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (shila == null)
            {
                return NotFound();
            }

            return View(shila);
        }

        // POST: Shilas/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var shila = await _context.Shilas.FindAsync(id);
            _context.Shilas.Remove(shila);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ShilaExists(int id)
        {
            return _context.Shilas.Any(e => e.Id == id);
        }
    }
}
