/* Laravel Filament Style CSS */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;

    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    --border-radius: 0.75rem;
    --border-radius-sm: 0.375rem;
    --border-radius-lg: 1rem;

    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Page Layout */
.filament-page {
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: 100vh;
    position: relative;
}

.filament-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    opacity: 0.03;
    z-index: 0;
}

.filament-page > * {
    position: relative;
    z-index: 1;
}

/* Header */
.filament-header {
    margin-bottom: 3rem;
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.filament-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.filament-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
}

.filament-header-heading {
    flex: 1;
}

.filament-header-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
    font-family: 'Cairo', sans-serif;
    letter-spacing: -0.025em;
}

.filament-header-icon {
    width: 2.5rem;
    height: 2.5rem;
    color: var(--primary-color);
    filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.2));
}

.filament-header-subtitle {
    color: var(--gray-600);
    font-size: 1rem;
    margin: 0;
    font-weight: 500;
    font-family: 'Cairo', sans-serif;
}

.filament-header-actions {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

/* Buttons */
.filament-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    border: 1px solid transparent;
    transition: var(--transition);
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.filament-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.filament-button:hover::before {
    left: 100%;
}

.filament-button-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-color: var(--primary-color);
}

.filament-button-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #3730a3 100%);
    border-color: var(--primary-dark);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.filament-button-secondary {
    background: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.filament-button-secondary:hover {
    background: var(--gray-50);
    color: var(--gray-800);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.filament-button-icon {
    width: 1.125rem;
    height: 1.125rem;
    flex-shrink: 0;
}

/* Stats Grid */
.filament-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.filament-stats-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    transition: var(--transition-slow);
}

.filament-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    opacity: 0;
    transition: var(--transition);
}

.filament-stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.filament-stats-card:hover::before {
    opacity: 1;
}

.filament-stats-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.filament-stats-icon {
    width: 4rem;
    height: 4rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.filament-stats-icon svg {
    width: 2rem;
    height: 2rem;
}

.filament-stats-icon-blue {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
}

.filament-stats-icon-green {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #15803d;
}

.filament-stats-icon-yellow {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #b45309;
}

.filament-stats-icon-red {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #b91c1c;
}

.filament-stats-icon-purple {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    color: #7c2d12;
}

.filament-stats-details {
    flex: 1;
}

.filament-stats-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    line-height: 1;
    font-family: 'Cairo', sans-serif;
    margin-bottom: 0.25rem;
}

.filament-stats-label {
    color: var(--gray-600);
    font-size: 0.95rem;
    font-weight: 500;
    font-family: 'Cairo', sans-serif;
}

/* Cards */
.filament-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-slow);
}

.filament-card:hover {
    box-shadow: var(--shadow-md);
}

.filament-card-header {
    padding: 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
}

.filament-card-header-content {
    flex: 1;
}

.filament-card-title {
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
    font-family: 'Cairo', sans-serif;
}

.filament-card-description {
    color: var(--gray-600);
    font-size: 0.95rem;
    margin: 0;
    font-weight: 500;
    font-family: 'Cairo', sans-serif;
}

.filament-card-header-actions {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

/* Search Input */
.filament-search-input {
    position: relative;
    display: flex;
    align-items: center;
}

.filament-search-icon {
    position: absolute;
    left: 1rem;
    width: 1.125rem;
    height: 1.125rem;
    color: var(--gray-400);
    z-index: 10;
    transition: var(--transition);
}

.filament-search-field {
    padding: 0.875rem 1rem 0.875rem 3rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    background: white;
    min-width: 300px;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.filament-search-field:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow);
}

.filament-search-field:focus + .filament-search-icon {
    color: var(--primary-color);
}

.filament-search-field::placeholder {
    color: var(--gray-500);
    font-weight: 400;
}

/* Table */
.filament-table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.filament-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.filament-table-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.filament-table-header-cell {
    padding: 1.25rem 1.5rem;
    text-align: right;
    font-weight: 700;
    color: var(--gray-800);
    border-bottom: 2px solid var(--gray-200);
    font-size: 0.875rem;
    font-family: 'Cairo', sans-serif;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    position: relative;
}

.filament-table-header-cell::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    opacity: 0;
    transition: var(--transition);
}

.filament-table-header-cell:hover::after {
    opacity: 1;
}

.filament-table-body {
    background: white;
}

.filament-table-row {
    transition: var(--transition);
    border-bottom: 1px solid var(--gray-100);
}

.filament-table-row:hover {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    transform: scale(1.001);
}

.filament-table-cell {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: top;
}

.filament-table-cell-content {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}

.filament-table-cell-primary {
    font-weight: 600;
    color: var(--gray-900);
    font-family: 'Cairo', sans-serif;
    font-size: 0.95rem;
}

.filament-table-cell-secondary {
    font-size: 0.825rem;
    color: var(--gray-600);
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

/* Table Actions */
.filament-table-actions {
    display: flex;
    gap: 0.5rem;
}

.filament-table-action {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    color: #6b7280;
    transition: all 0.2s ease;
    text-decoration: none;
}

.filament-table-action:hover {
    text-decoration: none;
}

.filament-table-action-view:hover {
    background-color: #dbeafe;
    color: #2563eb;
}

.filament-table-action-edit:hover {
    background-color: #fef3c7;
    color: #d97706;
}

.filament-table-action-delete:hover {
    background-color: #fee2e2;
    color: #dc2626;
}

.filament-table-action svg {
    width: 1rem;
    height: 1rem;
}

/* Badges */
.filament-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 1rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid transparent;
}

.filament-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.filament-badge:hover::before {
    left: 100%;
}

.filament-badge-primary {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border-color: #93c5fd;
}

.filament-badge-success {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border-color: #86efac;
}

.filament-badge-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-color: #fcd34d;
}

.filament-badge-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border-color: #f87171;
}

/* Empty State */
.filament-empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.filament-empty-state-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1rem;
    color: #d1d5db;
}

.filament-empty-state-icon svg {
    width: 100%;
    height: 100%;
}

.filament-empty-state-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 0.5rem 0;
}

.filament-empty-state-description {
    color: #6b7280;
    margin: 0 0 1.5rem 0;
}

/* Grid Container */
.filament-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

/* Ward Cards */
.filament-ward-card {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.3s ease;
}

.filament-ward-card-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.filament-ward-card-title h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.filament-ward-card-actions {
    display: flex;
    gap: 0.5rem;
}

.filament-ward-action {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    color: #6b7280;
    transition: all 0.2s ease;
    text-decoration: none;
}

.filament-ward-action:hover {
    background-color: #f3f4f6;
    color: #374151;
    text-decoration: none;
}

.filament-ward-action svg {
    width: 1rem;
    height: 1rem;
}

.filament-ward-card-content {
    padding: 0 1.5rem 1.5rem;
}

.filament-ward-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0 0 1rem 0;
}

.filament-ward-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.filament-ward-stat {
    text-align: center;
}

.filament-ward-stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.filament-ward-stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.filament-ward-progress {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filament-ward-progress-bar {
    flex: 1;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.filament-ward-progress-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.filament-progress-success {
    background-color: #10b981;
}

.filament-progress-warning {
    background-color: #f59e0b;
}

.filament-progress-danger {
    background-color: #ef4444;
}

.filament-ward-progress-text {
    font-size: 0.75rem;
    color: #6b7280;
    white-space: nowrap;
}

/* Room Cards */
.filament-room-card {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.3s ease;
}

.filament-room-card-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.filament-room-card-title h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.filament-room-card-actions {
    display: flex;
    gap: 0.5rem;
}

.filament-room-action {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    color: #6b7280;
    transition: all 0.2s ease;
    text-decoration: none;
}

.filament-room-action:hover {
    background-color: #f3f4f6;
    color: #374151;
    text-decoration: none;
}

.filament-room-action svg {
    width: 1rem;
    height: 1rem;
}

.filament-room-card-content {
    padding: 0 1.5rem 1.5rem;
}

.filament-room-ward {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background-color: #f9fafb;
    border-radius: 0.375rem;
}

.filament-room-ward-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.filament-room-ward-name {
    font-size: 0.875rem;
    color: #1f2937;
    font-weight: 600;
}

.filament-room-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0 0 1rem 0;
}

.filament-room-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.filament-room-stat {
    text-align: center;
}

.filament-room-stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.filament-room-stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.filament-room-progress {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filament-room-progress-bar {
    flex: 1;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.filament-room-progress-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.filament-room-progress-text {
    font-size: 0.75rem;
    color: #6b7280;
    white-space: nowrap;
}

/* Form Styles */
.filament-form {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.filament-section {
    background: white;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-slow);
    box-shadow: var(--shadow);
}

.filament-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.filament-section-header {
    padding: 2rem;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    position: relative;
}

.filament-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.filament-section-header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filament-section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
    font-family: 'Cairo', sans-serif;
}

.filament-section-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--primary-color);
}

.filament-section-description {
    color: var(--gray-600);
    font-size: 0.95rem;
    margin: 0;
    font-weight: 500;
    font-family: 'Cairo', sans-serif;
}

.filament-section-content {
    padding: 2rem;
}

.filament-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.filament-form-field {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    position: relative;
}

.filament-form-field-full {
    grid-column: 1 / -1;
}

.filament-form-label {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.95rem;
    font-family: 'Cairo', sans-serif;
    margin-bottom: 0.25rem;
}

.filament-form-label.required::after {
    content: " *";
    color: var(--danger-color);
    font-weight: 700;
}

.filament-form-input,
.filament-form-select,
.filament-form-textarea {
    padding: 1rem 1.25rem;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: var(--transition);
    background: white;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.filament-form-input:focus,
.filament-form-select:focus,
.filament-form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow);
    transform: translateY(-1px);
}

.filament-form-input::placeholder,
.filament-form-textarea::placeholder {
    color: var(--gray-500);
    font-weight: 400;
}

.filament-form-textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

.filament-form-error {
    color: var(--danger-color);
    font-size: 0.8rem;
    font-weight: 500;
    font-family: 'Cairo', sans-serif;
}

.filament-form-hint {
    color: var(--gray-600);
    font-size: 0.8rem;
    font-family: 'Cairo', sans-serif;
    font-weight: 400;
}

.filament-form-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filament-form-checkbox-input {
    width: 1rem;
    height: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    accent-color: #6366f1;
}

.filament-form-checkbox-label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
}

.filament-form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
    margin-top: 2rem;
    border-radius: 0.75rem;
}

.filament-notification {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.filament-notification-danger {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

/* Responsive */
@media (max-width: 768px) {
    .filament-page {
        padding: 1rem;
    }

    .filament-header-content {
        flex-direction: column;
        align-items: stretch;
    }

    .filament-stats-grid {
        grid-template-columns: 1fr;
    }

    .filament-grid-container {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .filament-card-header {
        flex-direction: column;
        align-items: stretch;
    }

    .filament-form-grid {
        grid-template-columns: 1fr;
    }

    .filament-form-actions {
        flex-direction: column;
    }
}
