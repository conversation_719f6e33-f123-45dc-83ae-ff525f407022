using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using PrisonManagementSystem.Models;
using System;
using System.Threading.Tasks;

namespace PrisonManagementSystem.Data
{
    public static class DbInitializer
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();

            // إنشاء الأدوار
            await CreateRoles(roleManager);

            // إنشاء المستخدم الافتراضي
            await CreateDefaultUser(userManager);
        }

        private static async Task CreateRoles(RoleManager<IdentityRole> roleManager)
        {
            string[] roleNames = { "Admin", "Manager", "Officer", "User" };

            foreach (var roleName in roleNames)
            {
                var roleExist = await roleManager.RoleExistsAsync(roleName);
                if (!roleExist)
                {
                    await roleManager.CreateAsync(new IdentityRole(roleName));
                }
            }
        }

        private static async Task CreateDefaultUser(UserManager<ApplicationUser> userManager)
        {
            var defaultUser = await userManager.FindByNameAsync("admin");
            
            if (defaultUser == null)
            {
                var user = new ApplicationUser
                {
                    UserName = "admin",
                    Email = "<EMAIL>",
                    FullName = "مدير النظام",
                    EmployeeNumber = "001",
                    Position = "مدير النظام",
                    Department = "تقنية المعلومات",
                    HireDate = DateTime.Now,
                    IsActive = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(user, "123");
                
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(user, "Admin");
                }
            }
        }
    }
}
