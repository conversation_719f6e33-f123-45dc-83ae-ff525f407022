@model PrisonManagementSystem.Models.Room

@{
    ViewData["Title"] = "تفاصيل الغرفة";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="filament-page filament-typography-enhanced">
    <!-- Header Section -->
    <div class="filament-header filament-header-enhanced">
        <div class="filament-header-content">
            <div class="filament-header-heading">
                <h1 class="filament-title-enhanced">
                    <svg class="filament-header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    الغرفة: @Model.RoomNumber
                </h1>
                <p class="filament-subtitle-enhanced">تفاصيل شاملة عن الغرفة والشيلات الموجودة بها</p>
            </div>
            <div class="filament-header-actions">
                <a href="@Url.Action("Edit", new { id = Model.Id })" class="filament-button filament-button-primary filament-button-enhanced">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    تعديل الغرفة
                </a>
                <a href="@Url.Action("Index")" class="filament-button filament-button-secondary filament-button-enhanced">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="filament-stats-grid">
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-blue">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.CurrentCount</div>
                    <div class="filament-stats-label">النزلاء الحاليين</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-green">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.MaxCapacity</div>
                    <div class="filament-stats-label">السعة القصوى</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-purple">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@(Model.MaxCapacity - Model.CurrentCount)</div>
                    <div class="filament-stats-label">أماكن متاحة</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-yellow">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    @{
                        var occupancyPercentage = Model.MaxCapacity > 0 ? (Model.CurrentCount * 100.0 / Model.MaxCapacity) : 0;
                    }
                    <div class="filament-stats-value">@occupancyPercentage.ToString("F1")%</div>
                    <div class="filament-stats-label">نسبة الإشغال</div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الغرفة -->
    <div class="filament-section">
        <div class="filament-section-header">
            <div class="filament-section-header-content">
                <h2 class="filament-section-title">
                    <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    معلومات الغرفة
                </h2>
                <p class="filament-section-description">البيانات الأساسية والتفصيلية للغرفة</p>
            </div>
        </div>
        
        <div class="filament-section-content">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="filament-detail-item">
                        <label class="filament-detail-label">رقم الغرفة</label>
                        <div class="filament-detail-value">@Model.RoomNumber</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="filament-detail-item">
                        <label class="filament-detail-label">العنبر</label>
                        <div class="filament-detail-value">@(Model.Ward?.Name ?? "غير محدد")</div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="filament-detail-item">
                        <label class="filament-detail-label">السعة القصوى</label>
                        <div class="filament-detail-value">@Model.MaxCapacity نزيل</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="filament-detail-item">
                        <label class="filament-detail-label">العدد الحالي</label>
                        <div class="filament-detail-value">@Model.CurrentCount نزيل</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="filament-detail-item">
                        <label class="filament-detail-label">الحالة</label>
                        <div class="filament-detail-value">
                            @if (Model.IsActive)
                            {
                                <span class="filament-badge filament-badge-success">نشطة</span>
                            }
                            else
                            {
                                <span class="filament-badge filament-badge-danger">غير نشطة</span>
                            }
                        </div>
                    </div>
                </div>
            </div>
            
            @if (!string.IsNullOrEmpty(Model.Description))
            {
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filament-detail-item">
                            <label class="filament-detail-label">الوصف</label>
                            <div class="filament-detail-value">@Model.Description</div>
                        </div>
                    </div>
                </div>
            }
            
            <!-- شريط التقدم -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="filament-detail-item">
                        <label class="filament-detail-label">مستوى الإشغال</label>
                        <div class="filament-room-progress">
                            <div class="filament-room-progress-bar">
                                @{
                                    var progressClass = occupancyPercentage > 80 ? "filament-progress-danger" : 
                                                      occupancyPercentage > 60 ? "filament-progress-warning" : "filament-progress-success";
                                }
                                <div class="filament-room-progress-fill @progressClass" style="width: @occupancyPercentage%"></div>
                            </div>
                            <span class="filament-room-progress-text">@occupancyPercentage.ToString("F1")% ممتلئة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشيلات في الغرفة -->
    <div class="filament-section">
        <div class="filament-section-header">
            <div class="filament-section-header-content">
                <h2 class="filament-section-title">
                    <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    الشيلات (غرف العقوبة) في الغرفة
                </h2>
                <p class="filament-section-description">قائمة الشيلات (غرف العقوبة) الموجودة في هذه الغرفة</p>
            </div>
            <div class="filament-section-actions">
                <a href="#" class="filament-button filament-button-primary filament-button-sm">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة شيلة جديدة
                </a>
            </div>
        </div>
        
        <div class="filament-section-content">
            @if (Model.Shilas != null && Model.Shilas.Any())
            {
                <div class="filament-grid-container">
                    @foreach (var shila in Model.Shilas)
                    {
                        <div class="filament-shila-card">
                            <div class="filament-shila-card-header">
                                <h4 class="filament-shila-card-title">@shila.Name</h4>
                                @if (shila.IsActive)
                                {
                                    <span class="filament-badge filament-badge-success">نشطة</span>
                                }
                                else
                                {
                                    <span class="filament-badge filament-badge-danger">غير نشطة</span>
                                }
                            </div>
                            
                            <div class="filament-shila-card-content">
                                @if (!string.IsNullOrEmpty(shila.Description))
                                {
                                    <p class="filament-shila-description">@shila.Description</p>
                                }
                                
                                <div class="filament-shila-stats">
                                    <div class="filament-shila-stat">
                                        <span class="filament-shila-stat-label">العدد الحالي</span>
                                        <span class="filament-shila-stat-value">@shila.CurrentCount</span>
                                    </div>
                                    <div class="filament-shila-stat">
                                        <span class="filament-shila-stat-label">السعة القصوى</span>
                                        <span class="filament-shila-stat-value">@shila.MaxCapacity</span>
                                    </div>
                                </div>
                                
                                <div class="filament-shila-progress">
                                    <div class="filament-shila-progress-bar">
                                        @{
                                            var shilaOccupancy = shila.MaxCapacity > 0 ? (shila.CurrentCount * 100.0 / shila.MaxCapacity) : 0;
                                            var shilaProgressClass = shilaOccupancy > 80 ? "filament-progress-danger" : 
                                                                   shilaOccupancy > 60 ? "filament-progress-warning" : "filament-progress-success";
                                        }
                                        <div class="filament-shila-progress-fill @shilaProgressClass" style="width: @shilaOccupancy%"></div>
                                    </div>
                                    <span class="filament-shila-progress-text">@shilaOccupancy.ToString("F1")% ممتلئة</span>
                                </div>
                            </div>
                            
                            <div class="filament-shila-card-actions">
                                <a href="#" class="filament-shila-action" title="عرض التفاصيل">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                                <a href="#" class="filament-shila-action" title="تعديل">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="filament-empty-state">
                    <div class="filament-empty-state-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="filament-empty-state-title">لا توجد شيلات في هذه الغرفة</h3>
                    <p class="filament-empty-state-description">ابدأ بإضافة أول شيلة في الغرفة</p>
                    <a href="#" class="filament-button filament-button-primary">
                        <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        إضافة شيلة جديدة
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.filament-shila-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            });
        });
    </script>
}
