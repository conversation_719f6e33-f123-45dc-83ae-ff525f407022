#pragma checksum "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c1"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Prisoners_Control), @"mvc.1.0.view", @"/Views/Prisoners/Control.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem

#nullable disable
    ;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c1", @"/Views/Prisoners/Control.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6b5cda3430a180463c664dbd4c2b7104438aceea37f75cdb0f39bc7283777da", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Prisoners_Control : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
       IEnumerable<PrisonManagementSystem.Models.Prisoner>

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "1", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "2", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "3", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "محكوم", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "موقوف", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "مبرأ", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "محبوس", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "عراقي", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "سوري", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "أردني", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("internalTransferForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "إفراج", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "مأمورية", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "علاج خارجي", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "تشغيل خارجي", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("externalTransferForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("takeActionForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("returnPrisonerForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/prisoners-control.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("\n");
#nullable restore
#line 3 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
  
    ViewData["Title"] = "كنترول النزلاء";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<style>
/* تنسيق الصفوف التي تم اتخاذ إجراء بشأنها */
.action-taken-row {
    background-color: #ffebee !important;
    border-left: 4px solid #f44336 !important;
}

.action-taken-row:hover {
    background-color: #ffcdd2 !important;
}

/* تنسيق النوافذ المنبثقة */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: bold;
    color: #495057;
}

.modal-body {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تنسيق الأزرار في قائمة الإجراءات */
.actions-menu .action-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: #495057;
    text-decoration: none;
    border: none;
    background: non");
            WriteLiteral(@"e;
    width: 100%;
    text-align: right;
    transition: background-color 0.15s ease-in-out;
}

.actions-menu .action-item:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.actions-menu .action-item i {
    margin-left: 0.5rem;
    width: 16px;
    text-align: center;
}

.actions-menu .action-item.danger:hover {
    background-color: #f8d7da;
    color: #721c24;
}

/* تنسيق أزرار النوافذ المنبثقة */
.modal-footer .btn {
    min-width: 100px;
    margin-left: 0.5rem;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* تنسيق الحقول المطلوبة */
.form-control:required:invalid {
    border-color: #dc3545;
}

.form-control:required:valid {
    border-color: #28a745;
}

/* تنسيق الخيارات المعطلة في القوائم المنسدلة */
option:disabled {
    color: ");
            WriteLiteral("#6c757d;\n    font-style: italic;\n}\n\n/* تحسين مظهر النوافذ المنبثقة على الشاشات الصغيرة */\n");
            WriteLiteral(@"@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-body {
        padding: 1rem;
    }
}
</style>

<!-- Page Header -->
<div class=""filament-page-header"">
    <div>
        <nav class=""filament-breadcrumbs"">
            <a");
            BeginWriteAttribute("href", " href=\"", 2561, "\"", 2596, 1);
            WriteAttributeValue("", 2568, 
#nullable restore
#line 142 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                      Url.Action("Index", "Home")

#line default
#line hidden
#nullable disable
            , 2568, 28, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-breadcrumb"">الرئيسية</a>
            <span class=""filament-breadcrumb-separator""><i class=""fas fa-chevron-left""></i></span>
            <span class=""filament-breadcrumb active"">كنترول النزلاء</span>
        </nav>
        <h1 class=""filament-page-title"">
            <i class=""fas fa-users""></i>
            كنترول النزلاء
        </h1>
    </div>
    <div class=""filament-page-actions"">
        <a");
            BeginWriteAttribute("href", " href=\"", 3012, "\"", 3053, 1);
            WriteAttributeValue("", 3019, 
#nullable restore
#line 152 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                  Url.Action("Create", "Prisoners")

#line default
#line hidden
#nullable disable
            , 3019, 34, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-btn filament-btn-primary"">
            <i class=""fas fa-plus""></i>
            إضافة نزيل جديد
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class=""filament-search-bar"">
    <div class=""filament-search-row"">
        <div class=""filament-search-field"">
            <label>البحث العام</label>
            <input type=""text"" id=""globalSearch"" placeholder=""البحث بالاسم، الرقم الوطني، رقم السجين..."" class=""form-control"">
        </div>
        <div class=""filament-search-field"">
            <label>العنبر</label>
            <select id=""wardFilter"" class=""form-control"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c115082", async() => {
                WriteLiteral("جميع العنابر");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c116286", async() => {
                WriteLiteral("العنبر الأول");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c117490", async() => {
                WriteLiteral("العنبر الثاني");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c118695", async() => {
                WriteLiteral("العنبر الثالث");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n            </select>\n        </div>\n        <div class=\"filament-search-field\">\n            <label>الحالة</label>\n            <select id=\"statusFilter\" class=\"form-control\">\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c120086", async() => {
                WriteLiteral("جميع الحالات");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c121290", async() => {
                WriteLiteral("محكوم");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_4.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c122487", async() => {
                WriteLiteral("موقوف");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_5.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_5);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c123684", async() => {
                WriteLiteral("مبرأ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_6.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </select>
        </div>
        <div class=""filament-search-actions"">
            <button type=""button"" class=""filament-btn filament-btn-primary"" onclick=""applyFilters()"">
                <i class=""fas fa-search""></i>
                بحث
            </button>
            <button type=""button"" class=""filament-btn filament-btn-secondary"" onclick=""clearFilters()"">
                <i class=""fas fa-times""></i>
                مسح
            </button>
        </div>
    </div>
</div>
                    <div class=""filters-grid"">
                        <div class=""filter-group"">
                            <label class=""filter-label"">البحث بالاسم</label>
                            <div class=""input-wrapper"">
                                <i class=""fas fa-search input-icon""></i>
                                <input type=""text"" id=""searchName"" class=""filament-input"" placeholder=""اكتب اسم النزيل..."">
                            </div>
                        </div>
                        <div cla");
            WriteLiteral(@"ss=""filter-group"">
                            <label class=""filter-label"">الرقم الوطني</label>
                            <div class=""input-wrapper"">
                                <i class=""fas fa-id-card input-icon""></i>
                                <input type=""text"" id=""searchNational"" class=""filament-input"" placeholder=""الرقم الوطني..."">
                            </div>
                        </div>
                        <div class=""filter-group"">
                            <label class=""filter-label"">رقم القضية</label>
                            <div class=""input-wrapper"">
                                <i class=""fas fa-gavel input-icon""></i>
                                <input type=""text"" id=""searchCase"" class=""filament-input"" placeholder=""رقم القضية..."">
                            </div>
                        </div>
                        <div class=""filter-group"">
                            <label class=""filter-label"">العنبر</label>
                            <select id=""filter");
            WriteLiteral("Ward\" class=\"filament-select\">\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c127114", async() => {
                WriteLiteral("جميع العنابر");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c128334", async() => {
                WriteLiteral("العنبر الأول");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c129554", async() => {
                WriteLiteral("العنبر الثاني");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c130775", async() => {
                WriteLiteral("العنبر الثالث");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                            </select>
                        </div>
                        <div class=""filter-group"">
                            <label class=""filter-label"">الحالة</label>
                            <select id=""filterStatus"" class=""filament-select"">
                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c132274", async() => {
                WriteLiteral("جميع الحالات");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c133494", async() => {
                WriteLiteral("محبوس");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_7.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c134707", async() => {
                WriteLiteral("موقوف");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_5.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_5);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c135920", async() => {
                WriteLiteral("مبرأ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_6.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                            </select>
                        </div>
                        <div class=""filter-group"">
                            <label class=""filter-label"">الجنسية</label>
                            <select id=""filterNationality"" class=""filament-select"">
                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c137416", async() => {
                WriteLiteral("جميع الجنسيات");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c138637", async() => {
                WriteLiteral("عراقي");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c139850", async() => {
                WriteLiteral("سوري");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_9.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_9);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c141062", async() => {
                WriteLiteral("أردني");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_10.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_10);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                            </select>
                        </div>
                    </div>
                    <div class=""filters-actions"">
                        <button type=""button"" class=""filament-btn filament-btn-primary"" onclick=""searchPrisoners()"">
                            <i class=""fas fa-search""></i>
                            تطبيق الفلاتر
                        </button>
                        <button type=""button"" class=""filament-btn filament-btn-secondary"" onclick=""clearSearch()"">
                            <i class=""fas fa-refresh""></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>

            <!-- Data Table Card -->
            <div class=""filament-card table-card"">
                <div class=""card-header"">
                    <div class=""table-header-left"">
                        <h3 class=""card-title"">
                            <i class=""fas fa-table""></i>
                     ");
            WriteLiteral("       قائمة النزلاء\n                        </h3>\n                        <span class=\"records-count\">\n                            <i class=\"fas fa-users\"></i>\n                            إجمالي <strong id=\"totalResults\">");
            Write(
#nullable restore
#line 269 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                              Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</strong> نزيل
                        </span>
                    </div>
                    <div class=""table-header-right"">
                        <div class=""table-actions"">
                            <button class=""filament-btn filament-btn-outline"" onclick=""exportToExcel()"">
                                <i class=""fas fa-download""></i>
                                تصدير
                            </button>
                            <button class=""filament-btn filament-btn-outline"" onclick=""printResults()"">
                                <i class=""fas fa-print""></i>
                                طباعة
                            </button>
                            <div class=""view-toggle"">
                                <button class=""view-btn active"" data-view=""table"">
                                    <i class=""fas fa-table""></i>
                                </button>
                                <button class=""view-btn"" data-view=""grid"">
                                    <i c");
            WriteLiteral(@"lass=""fas fa-th-large""></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class=""table-wrapper"">
                    <div class=""filament-table-container"">
                        <table class=""filament-table"" id=""prisonersTable"">
                            <thead>
                                <tr>
                                    <th class=""table-checkbox"">
                                        <input type=""checkbox"" class=""filament-checkbox"" id=""selectAll"">
                                    </th>
                                    <th class=""sortable"" data-sort=""name"">
                                        <div class=""th-content"">
                                            <span>الاسم الكامل</span>
                                            <i class=""fas fa-sort sort-icon""></i>
                                        </div>
                                    ");
            WriteLiteral(@"</th>
                                    <th class=""sortable"" data-sort=""national"">
                                        <div class=""th-content"">
                                            <span>الرقم الوطني</span>
                                            <i class=""fas fa-sort sort-icon""></i>
                                        </div>
                                    </th>
                                    <th class=""sortable"" data-sort=""birth"">
                                        <div class=""th-content"">
                                            <span>تاريخ الميلاد</span>
                                            <i class=""fas fa-sort sort-icon""></i>
                                        </div>
                                    </th>
                                    <th>الجنسية</th>
                                    <th>العنبر</th>
                                    <th>الغرفة</th>
                                    <th>التهمة</th>
                                    <th c");
            WriteLiteral(@"lass=""sortable"" data-sort=""entry"">
                                        <div class=""th-content"">
                                            <span>تاريخ الدخول</span>
                                            <i class=""fas fa-sort sort-icon""></i>
                                        </div>
                                    </th>
                                    <th>الحالة</th>
                                    <th class=""actions-header"">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id=""prisonersTableBody"">
");
#nullable restore
#line 335 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                 foreach (var prisoner in Model)
                                {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                    <tr class=\"table-row\" data-prisoner-id=\"");
            Write(
#nullable restore
#line 337 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                             prisoner.Id

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("\">\n                                        <td class=\"table-checkbox\">\n                                            <input type=\"checkbox\" class=\"filament-checkbox row-checkbox\"");
            BeginWriteAttribute("value", " value=\"", 13037, "\"", 13057, 1);
            WriteAttributeValue("", 13045, 
#nullable restore
#line 339 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                                  prisoner.Id

#line default
#line hidden
#nullable disable
            , 13045, 12, false);
            EndWriteAttribute();
            WriteLiteral(@">
                                        </td>
                                        <td class=""prisoner-cell"">
                                            <div class=""prisoner-info"">
                                                <div class=""prisoner-avatar"">
                                                    <i class=""fas fa-user""></i>
                                                </div>
                                                <div class=""prisoner-details"">
                                                    <div class=""prisoner-name"">");
            Write(
#nullable restore
#line 347 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                prisoner.FullName

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                                    <div class=\"prisoner-number\">#");
            Write(
#nullable restore
#line 348 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                   prisoner.PrisonerNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class=""national-cell"">
                                            <span class=""national-number"">");
            Write(
#nullable restore
#line 353 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                           prisoner.NationalNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</span>\n                                        </td>\n                                        <td class=\"date-cell\">\n                                            <span class=\"date-text\">");
            Write(
#nullable restore
#line 356 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                     prisoner.DateOfBirth.ToString("yyyy/MM/dd")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</span>\n                                            <span class=\"age-text\">");
            Write(
#nullable restore
#line 357 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                    prisoner.Age

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(" سنة</span>\n                                        </td>\n                                        <td class=\"nationality-cell\">\n                                            <span class=\"nationality-badge\">");
            Write(
#nullable restore
#line 360 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                             prisoner.Nationality

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</span>\n                                        </td>\n                                        <td class=\"ward-cell\">\n");
#nullable restore
#line 363 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                             if (prisoner.Ward != null)
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <span class=\"filament-badge filament-badge-primary\">\n                                                    <i class=\"fas fa-building\"></i>\n                                                    ");
            Write(
#nullable restore
#line 367 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                     prisoner.Ward.Name

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("\n                                                </span>\n");
#nullable restore
#line 369 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                            }
                                            else
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <span class=\"filament-badge filament-badge-gray\">غير محدد</span>\n");
#nullable restore
#line 373 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                            }

#line default
#line hidden
#nullable disable

            WriteLiteral("                                        </td>\n                                        <td class=\"room-cell\">\n");
#nullable restore
#line 376 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                             if (prisoner.Room != null)
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <span class=\"filament-badge filament-badge-success\">\n                                                    <i class=\"fas fa-door-open\"></i>\n                                                    ");
            Write(
#nullable restore
#line 380 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                     prisoner.Room.RoomNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("\n                                                </span>\n");
#nullable restore
#line 382 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                            }
                                            else
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <span class=\"filament-badge filament-badge-gray\">غير محدد</span>\n");
#nullable restore
#line 386 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                            }

#line default
#line hidden
#nullable disable

            WriteLiteral("                                        </td>\n                                        <td class=\"charge-cell\">\n");
#nullable restore
#line 389 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                             if (prisoner.Cases != null && prisoner.Cases.Any())
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <div class=\"charge-info\">\n                                                    <span class=\"charge-text\">");
            Write(
#nullable restore
#line 392 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                               prisoner.Cases.FirstOrDefault()?.Charge

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</span>\n");
#nullable restore
#line 393 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                     if (prisoner.Cases.Count() > 1)
                                                    {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                        <span class=\"more-cases\">+");
            Write(
#nullable restore
#line 395 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                    prisoner.Cases.Count() - 1

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(" أخرى</span>\n");
#nullable restore
#line 396 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                    }

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                </div>\n");
#nullable restore
#line 398 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                            }
                                            else
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <span class=\"filament-badge filament-badge-gray\">لا توجد قضايا</span>\n");
#nullable restore
#line 402 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                            }

#line default
#line hidden
#nullable disable

            WriteLiteral("                                        </td>\n                                        <td class=\"date-cell\">\n                                            <span class=\"date-text\">");
            Write(
#nullable restore
#line 405 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                     prisoner.EntryDate.ToString("yyyy/MM/dd")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</span>\n                                        </td>\n                                        <td class=\"status-cell\">\n");
#nullable restore
#line 408 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                              
                                                var status = "محبوس";
                                                var statusClass = "filament-badge-success";
                                                if (prisoner.ExpectedReleaseDate.HasValue && prisoner.ExpectedReleaseDate.Value <= DateTime.Now)
                                                {
                                                    status = "مستحق الإفراج";
                                                    statusClass = "filament-badge-warning";
                                                }
                                            

#line default
#line hidden
#nullable disable

            WriteLiteral("                                            <span");
            BeginWriteAttribute("class", " class=\"", 18496, "\"", 18531, 2);
            WriteAttributeValue("", 18504, "filament-badge", 18504, 14, true);
            WriteAttributeValue(" ", 18518, 
#nullable restore
#line 417 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                         statusClass

#line default
#line hidden
#nullable disable
            , 18519, 12, false);
            EndWriteAttribute();
            WriteLiteral(">\n                                                <i class=\"fas fa-circle status-dot\"></i>\n                                                ");
            Write(
#nullable restore
#line 419 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                 status

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"
                                            </span>
                                        </td>
                                        <td class=""actions-cell"">
                                            <div class=""table-actions-dropdown"">
                                                <button class=""actions-trigger"" onclick=""toggleActionsMenu(this)"">
                                                    <i class=""fas fa-ellipsis-v""></i>
                                                </button>
                                                <div class=""actions-menu"">
                                                    <a");
            BeginWriteAttribute("href", " href=\"", 19312, "\"", 19350, 2);
            WriteAttributeValue("", 19319, "/Prisoners/Details/", 19319, 19, true);
            WriteAttributeValue("", 19338, 
#nullable restore
#line 428 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                 prisoner.Id

#line default
#line hidden
#nullable disable
            , 19338, 12, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""action-item"">
                                                        <i class=""fas fa-eye""></i>
                                                        عرض التفاصيل
                                                    </a>
                                                    <a");
            BeginWriteAttribute("href", " href=\"", 19636, "\"", 19671, 2);
            WriteAttributeValue("", 19643, "/Prisoners/Edit/", 19643, 16, true);
            WriteAttributeValue("", 19659, 
#nullable restore
#line 432 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                              prisoner.Id

#line default
#line hidden
#nullable disable
            , 19659, 12, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""action-item"">
                                                        <i class=""fas fa-edit""></i>
                                                        تعديل بيانات النزيل
                                                    </a>
                                                    <div class=""action-divider""></div>

");
#nullable restore
#line 438 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                      
                                                        // تحديد حالة النزيل - هل هو خارج السجن أم لا
                                                        var isOutside = false;
                                                        var latestMovement = prisoner.Movements?.Where(m => m.Status == "نشطة" && m.MovementType == "خارجي").OrderByDescending(m => m.MovementDate).FirstOrDefault();
                                                        if (latestMovement != null && latestMovement.ActualReturnDate == null)
                                                        {
                                                            isOutside = true;
                                                        }
                                                    

#line default
#line hidden
#nullable disable

            WriteLiteral("\n");
#nullable restore
#line 448 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                     if (isOutside)
                                                    {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                        <button class=\"action-item\"");
            BeginWriteAttribute("onclick", " onclick=\"", 21030, "\"", 21068, 3);
            WriteAttributeValue("", 21040, "returnPrisoner(", 21040, 15, true);
            WriteAttributeValue("", 21055, 
#nullable restore
#line 450 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                             prisoner.Id

#line default
#line hidden
#nullable disable
            , 21055, 12, false);
            WriteAttributeValue("", 21067, ")", 21067, 1, true);
            EndWriteAttribute();
            WriteLiteral(">\n                                                            <i class=\"fas fa-undo\"></i>\n                                                            إعادة النزيل\n                                                        </button>\n");
#nullable restore
#line 454 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                    }
                                                    else
                                                    {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                        <button class=\"action-item\"");
            BeginWriteAttribute("onclick", " onclick=\"", 21546, "\"", 21586, 3);
            WriteAttributeValue("", 21556, "internalTransfer(", 21556, 17, true);
            WriteAttributeValue("", 21573, 
#nullable restore
#line 457 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                               prisoner.Id

#line default
#line hidden
#nullable disable
            , 21573, 12, false);
            WriteAttributeValue("", 21585, ")", 21585, 1, true);
            EndWriteAttribute();
            WriteLiteral(@">
                                                            <i class=""fas fa-exchange-alt""></i>
                                                            نقل داخلي
                                                        </button>
                                                        <button class=""action-item""");
            BeginWriteAttribute("onclick", " onclick=\"", 21904, "\"", 21944, 3);
            WriteAttributeValue("", 21914, "externalTransfer(", 21914, 17, true);
            WriteAttributeValue("", 21931, 
#nullable restore
#line 461 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                               prisoner.Id

#line default
#line hidden
#nullable disable
            , 21931, 12, false);
            WriteAttributeValue("", 21943, ")", 21943, 1, true);
            EndWriteAttribute();
            WriteLiteral(">\n                                                            <i class=\"fas fa-sign-out-alt\"></i>\n                                                            نقل خارجي\n                                                        </button>\n");
#nullable restore
#line 465 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                    }

#line default
#line hidden
#nullable disable

            WriteLiteral("\n                                                    <button class=\"action-item\"");
            BeginWriteAttribute("onclick", " onclick=\"", 22313, "\"", 22347, 3);
            WriteAttributeValue("", 22323, "takeAction(", 22323, 11, true);
            WriteAttributeValue("", 22334, 
#nullable restore
#line 467 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                     prisoner.Id

#line default
#line hidden
#nullable disable
            , 22334, 12, false);
            WriteAttributeValue("", 22346, ")", 22346, 1, true);
            EndWriteAttribute();
            WriteLiteral(@">
                                                        <i class=""fas fa-clipboard-check""></i>
                                                        اتخاذ إجراء
                                                    </button>
                                                    <button class=""action-item""");
            BeginWriteAttribute("onclick", " onclick=\"", 22654, "\"", 22685, 3);
            WriteAttributeValue("", 22664, "addCase(", 22664, 8, true);
            WriteAttributeValue("", 22672, 
#nullable restore
#line 471 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                  prisoner.Id

#line default
#line hidden
#nullable disable
            , 22672, 12, false);
            WriteAttributeValue("", 22684, ")", 22684, 1, true);
            EndWriteAttribute();
            WriteLiteral(@">
                                                        <i class=""fas fa-gavel""></i>
                                                        إضافة قضية
                                                    </button>
                                                    <div class=""action-divider""></div>
                                                    <button class=""action-item danger""");
            BeginWriteAttribute("onclick", " onclick=\"", 23075, "\"", 23113, 3);
            WriteAttributeValue("", 23085, "deletePrisoner(", 23085, 15, true);
            WriteAttributeValue("", 23100, 
#nullable restore
#line 476 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                                prisoner.Id

#line default
#line hidden
#nullable disable
            , 23100, 12, false);
            WriteAttributeValue("", 23112, ")", 23112, 1, true);
            EndWriteAttribute();
            WriteLiteral(@">
                                                        <i class=""fas fa-trash""></i>
                                                        حذف
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
");
#nullable restore
#line 484 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                }

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class=""table-pagination"">
                    <div class=""pagination-info"">
                        <span>عرض <strong>1</strong> إلى <strong>");
            Write(
#nullable restore
#line 493 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                  Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</strong> من <strong>");
            Write(
#nullable restore
#line 493 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                                                     Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</strong> نتيجة</span>
                    </div>
                    <div class=""pagination-controls"">
                        <button class=""pagination-btn"" disabled>
                            <i class=""fas fa-chevron-right""></i>
                        </button>
                        <span class=""pagination-numbers"">
                            <button class=""pagination-number active"">1</button>
                        </span>
                        <button class=""pagination-btn"" disabled>
                            <i class=""fas fa-chevron-left""></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // تحديث التاريخ والوقت
    function updateDateTime() {
        const now = new Date();
        const dateStr = now.toLocaleDateString('ar-SA');
        const timeStr = now.toLocaleTimeString('ar-SA');

        document.getElementById('currentDate').textContent = dateStr;
        document.getElementById('c");
            WriteLiteral(@"urrentTime').textContent = timeStr;
    }

    // تحديث كل ثانية
    setInterval(updateDateTime, 1000);
    updateDateTime();

    // وظائف البحث والفلترة
    function searchPrisoners() {
        const name = document.getElementById('searchName').value;
        const national = document.getElementById('searchNational').value;
        const caseNum = document.getElementById('searchCase').value;
        const ward = document.getElementById('filterWard').value;
        const status = document.getElementById('filterStatus').value;

        // تطبيق الفلترة على الجدول
        const rows = document.querySelectorAll('#prisonersTableBody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            let show = true;

            if (name && !row.querySelector('.prisoner-name').textContent.includes(name)) {
                show = false;
            }

            if (national && !row.cells[2].textContent.includes(national)) {
                show = false;
            }

            if (show) {
        ");
            WriteLiteral(@"        row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        document.getElementById('totalResults').textContent = visibleCount;
    }

    function clearSearch() {
        document.getElementById('searchName').value = '';
        document.getElementById('searchNational').value = '';
        document.getElementById('searchCase').value = '';
        document.getElementById('filterWard').value = '';
        document.getElementById('filterStatus').value = '';

        const rows = document.querySelectorAll('#prisonersTableBody tr');
        rows.forEach(row => {
            row.style.display = '';
        });

        document.getElementById('totalResults').textContent = '");
            Write(
#nullable restore
#line 573 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"';
    }

    // وظائف الإجراءات
    function viewPrisoner(id) {
        window.location.href = '/Prisoners/Details/' + id;
    }

    function editPrisoner(id) {
        window.location.href = '/Prisoners/Edit/' + id;
    }

    function movePrisoner(id) {
        if (confirm('هل تريد نقل هذا النزيل؟')) {
            // تنفيذ عملية النقل
            alert('سيتم تطوير هذه الميزة قريباً');
        }
    }

    function exportToExcel() {
        alert('سيتم تصدير البيانات إلى Excel');
    }

    function printResults() {
        window.print();
    }
</script>

<!-- Modal للنقل الداخلي -->
<div class=""modal fade"" id=""internalTransferModal"" tabindex=""-1"" role=""dialog"">
    <div class=""modal-dialog"" role=""document"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">نقل داخلي</h5>
                <button type=""button"" class=""close"" data-dismiss=""modal"">
                    <span>&times;</span>
                </button>
            </div>
            ");
            WriteLiteral("<div class=\"modal-body\">\n                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c178317", async() => {
                WriteLiteral(@"
                    <input type=""hidden"" id=""transferPrisonerId"" />

                    <div class=""form-group"">
                        <label for=""newWardId"">العنبر الجديد:</label>
                        <select class=""form-control"" id=""newWardId"" required>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c178900", async() => {
                    WriteLiteral("اختر العنبر");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                        </select>
                    </div>

                    <div class=""form-group"">
                        <label for=""newRoomId"">الغرفة الجديدة:</label>
                        <select class=""form-control"" id=""newRoomId"" required>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c180443", async() => {
                    WriteLiteral("اختر الغرفة");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                        </select>
                    </div>

                    <div class=""form-group"">
                        <label for=""transferReason"">سبب النقل:</label>
                        <textarea class=""form-control"" id=""transferReason"" rows=""3"" placeholder=""اكتب سبب النقل...""></textarea>
                    </div>
                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-dismiss=""modal"">إلغاء</button>
                <button type=""button"" class=""btn btn-primary"" onclick=""submitInternalTransfer()"">تأكيد النقل</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal للنقل الخارجي -->
<div class=""modal fade"" id=""externalTransferModal"" tabindex=""-1"" role=""dialog"">
    <div class=""modal-dialog modal-lg"" role=""document"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">نقل خارجي</h5>
                <button type=""button"" class=""close"" data-dismiss=""modal"">
                    <span>&times;</span>
                </button>
            </div>
            <div class=""modal-body"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c183987", async() => {
                WriteLiteral(@"
                    <input type=""hidden"" id=""externalTransferPrisonerId"" />

                    <div class=""form-group"">
                        <label for=""movementType"">نوع النقل:</label>
                        <select class=""form-control"" id=""movementType"" required onchange=""toggleMovementFields()"">
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c184616", async() => {
                    WriteLiteral("اختر نوع النقل");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c185898", async() => {
                    WriteLiteral("إفراج");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_12.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c187173", async() => {
                    WriteLiteral("مأمورية");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_13.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c188450", async() => {
                    WriteLiteral("علاج خارجي");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_14.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c189730", async() => {
                    WriteLiteral("تشغيل خارجي");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_15.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_15);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                        </select>
                    </div>

                    <div class=""form-group"">
                        <label for=""destination"">الوجهة:</label>
                        <input type=""text"" class=""form-control"" id=""destination"" placeholder=""مكان الوجهة..."" />
                    </div>

                    <div class=""form-group"" id=""returnDateGroup"" style=""display: none;"">
                        <label for=""expectedReturnDate"">تاريخ العودة المتوقع:</label>
                        <input type=""datetime-local"" class=""form-control"" id=""expectedReturnDate"" />
                    </div>

                    <div class=""form-group"">
                        <label for=""movementReason"">السبب:</label>
                        <textarea class=""form-control"" id=""movementReason"" rows=""3"" placeholder=""اكتب سبب النقل...""></textarea>
                    </div>

                    <div class=""form-group"">
                        <label for=""movementNotes"">ملاحظات:</label>
                        <");
                WriteLiteral("textarea class=\"form-control\" id=\"movementNotes\" rows=\"2\" placeholder=\"ملاحظات إضافية...\"></textarea>\n                    </div>\n                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-dismiss=""modal"">إلغاء</button>
                <button type=""button"" class=""btn btn-primary"" onclick=""submitExternalTransfer()"">تأكيد النقل</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لاتخاذ إجراء -->
<div class=""modal fade"" id=""takeActionModal"" tabindex=""-1"" role=""dialog"">
    <div class=""modal-dialog"" role=""document"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">اتخاذ إجراء</h5>
                <button type=""button"" class=""close"" data-dismiss=""modal"">
                    <span>&times;</span>
                </button>
            </div>
            <div class=""modal-body"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c194156", async() => {
                WriteLiteral(@"
                    <input type=""hidden"" id=""actionPrisonerId"" />

                    <div class=""form-group"">
                        <label for=""actionDescription"">وصف الإجراء:</label>
                        <textarea class=""form-control"" id=""actionDescription"" rows=""4"" placeholder=""اكتب تفاصيل الإجراء المتخذ..."" required></textarea>
                    </div>

                    <div class=""form-group"">
                        <label for=""actionDate"">تاريخ الإجراء:</label>
                        <input type=""datetime-local"" class=""form-control"" id=""actionDate"" required />
                    </div>
                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-dismiss=""modal"">إلغاء</button>
                <button type=""button"" class=""btn btn-danger"" onclick=""submitAction()"">حفظ الإجراء</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإعادة النزيل -->
<div class=""modal fade"" id=""returnPrisonerModal"" tabindex=""-1"" role=""dialog"">
    <div class=""modal-dialog"" role=""document"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">إعادة النزيل</h5>
                <button type=""button"" class=""close"" data-dismiss=""modal"">
                    <span>&times;</span>
                </button>
            </div>
            <div class=""modal-body"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c197005", async() => {
                WriteLiteral(@"
                    <input type=""hidden"" id=""returnPrisonerId"" />

                    <div class=""form-group"">
                        <label for=""returnWardId"">العنبر:</label>
                        <select class=""form-control"" id=""returnWardId"" required>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c197585", async() => {
                    WriteLiteral("اختر العنبر");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                        </select>
                    </div>

                    <div class=""form-group"">
                        <label for=""returnRoomId"">الغرفة:</label>
                        <select class=""form-control"" id=""returnRoomId"" required>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c199126", async() => {
                    WriteLiteral("اختر الغرفة");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                        </select>
                    </div>

                    <div class=""form-group"">
                        <label for=""returnNotes"">ملاحظات الإعادة:</label>
                        <textarea class=""form-control"" id=""returnNotes"" rows=""3"" placeholder=""ملاحظات حول إعادة النزيل...""></textarea>
                    </div>
                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-dismiss=""modal"">إلغاء</button>
                <button type=""button"" class=""btn btn-success"" onclick=""submitReturnPrisoner()"">تأكيد الإعادة</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let currentPrisonerId = null;

// تحميل العنابر عند تحميل الصفحة
$(document).ready(function() {
    loadWards();

    // تعيين التاريخ الحالي لحقل تاريخ الإجراء
    $('#actionDate').val(new Date().toISOString().slice(0, 16));
});

// تحميل العنابر
function loadWards() {
    $.get('/Prisoners/GetWards', function(data) {
        const wardSelects = ['#newWardId', '#returnWardId'];
        wardSelects.forEach(selector => {
            $(selector).empty().append('<option value="""">اختر العنبر</option>');
            data.forEach(ward => {
                $(selector).append(`<option value=""${ward.id}"">${ward.name}</option>`);
            });
        });
    });
}

//");
            WriteLiteral(@" تحميل الغرف حسب العنبر
function loadRoomsByWard(wardId, roomSelectId) {
    if (!wardId) {
        $(roomSelectId).empty().append('<option value="""">اختر الغرفة</option>');
        return;
    }

    $.get('/Prisoners/GetRoomsByWard', { wardId: wardId }, function(data) {
        $(roomSelectId).empty().append('<option value="""">اختر الغرفة</option>');
        data.forEach(room => {
            const available = room.available > 0 ? `(متاح: ${room.available})` : '(ممتلئة)';
            const disabled = room.available <= 0 ? 'disabled' : '';
            $(roomSelectId).append(`<option value=""${room.id}"" ${disabled}>غرفة ${room.roomNumber} ${available}</option>`);
        });
    });
}

// أحداث تغيير العنبر
$('#newWardId').change(function() {
    loadRoomsByWard($(this).val(), '#newRoomId');
});

$('#returnWardId').change(function() {
    loadRoomsByWard($(this).val(), '#returnRoomId');
});

// النقل الداخلي
function internalTransfer(prisonerId) {
    currentPrisonerId = prisonerId;
    $('#transferPrisonerId').");
            WriteLiteral(@"val(prisonerId);
    $('#internalTransferModal').modal('show');
}

function submitInternalTransfer() {
    const formData = {
        prisonerId: $('#transferPrisonerId').val(),
        newWardId: $('#newWardId').val(),
        newRoomId: $('#newRoomId').val(),
        reason: $('#transferReason').val(),
        __RequestVerificationToken: $('input[name=""__RequestVerificationToken""]').val()
    };

    if (!formData.newWardId || !formData.newRoomId) {
        alert('يرجى اختيار العنبر والغرفة');
        return;
    }

    $.post('/Prisoners/InternalTransfer', formData, function(response) {
        if (response.success) {
            alert(response.message);
            $('#internalTransferModal').modal('hide');
            location.reload(); // إعادة تحميل الصفحة لإظهار التغييرات
        } else {
            alert('خطأ: ' + response.message);
        }
    }).fail(function() {
        alert('حدث خطأ في الاتصال');
    });
}

// النقل الخارجي
function externalTransfer(prisonerId) {
    currentPrisonerId = priso");
            WriteLiteral(@"nerId;
    $('#externalTransferPrisonerId').val(prisonerId);
    $('#externalTransferModal').modal('show');
}

function toggleMovementFields() {
    const movementType = $('#movementType').val();
    const returnDateGroup = $('#returnDateGroup');

    // إظهار حقل تاريخ العودة للأنواع التي تتطلب عودة
    if (movementType === 'مأمورية' || movementType === 'علاج خارجي' || movementType === 'تشغيل خارجي') {
        returnDateGroup.show();
        $('#expectedReturnDate').prop('required', true);
    } else {
        returnDateGroup.hide();
        $('#expectedReturnDate').prop('required', false);
    }
}

function submitExternalTransfer() {
    const formData = {
        prisonerId: $('#externalTransferPrisonerId').val(),
        movementType: $('#movementType').val(),
        destination: $('#destination').val(),
        expectedReturnDate: $('#expectedReturnDate').val() || null,
        reason: $('#movementReason').val(),
        notes: $('#movementNotes').val(),
        __RequestVerificationToken: $('input[name");
            WriteLiteral(@"=""__RequestVerificationToken""]').val()
    };

    if (!formData.movementType) {
        alert('يرجى اختيار نوع النقل');
        return;
    }

    $.post('/Prisoners/ExternalTransfer', formData, function(response) {
        if (response.success) {
            alert(response.message);
            $('#externalTransferModal').modal('hide');
            location.reload();
        } else {
            alert('خطأ: ' + response.message);
        }
    }).fail(function() {
        alert('حدث خطأ في الاتصال');
    });
}

// اتخاذ إجراء
function takeAction(prisonerId) {
    currentPrisonerId = prisonerId;
    $('#actionPrisonerId').val(prisonerId);
    $('#takeActionModal').modal('show');
}

function submitAction() {
    const formData = {
        prisonerId: $('#actionPrisonerId').val(),
        actionDescription: $('#actionDescription').val(),
        actionDate: $('#actionDate').val(),
        __RequestVerificationToken: $('input[name=""__RequestVerificationToken""]').val()
    };

    if (!formData.actionDescription");
            WriteLiteral(@".trim()) {
        alert('يرجى كتابة وصف الإجراء');
        return;
    }

    $.post('/Prisoners/TakeAction', formData, function(response) {
        if (response.success) {
            alert(response.message);
            $('#takeActionModal').modal('hide');

            // تغيير لون الصف إلى أحمر
            const row = $(`tr[data-prisoner-id=""${formData.prisonerId}""]`);
            row.addClass('action-taken-row');

            // إعادة تحميل الصفحة بعد ثانيتين لإظهار التغييرات
            setTimeout(() => location.reload(), 2000);
        } else {
            alert('خطأ: ' + response.message);
        }
    }).fail(function() {
        alert('حدث خطأ في الاتصال');
    });
}

// إعادة النزيل
function returnPrisoner(prisonerId) {
    currentPrisonerId = prisonerId;
    $('#returnPrisonerId').val(prisonerId);
    $('#returnPrisonerModal').modal('show');
}

function submitReturnPrisoner() {
    const formData = {
        prisonerId: $('#returnPrisonerId').val(),
        wardId: $('#returnWardId').val(),
     ");
            WriteLiteral(@"   roomId: $('#returnRoomId').val(),
        notes: $('#returnNotes').val(),
        __RequestVerificationToken: $('input[name=""__RequestVerificationToken""]').val()
    };

    if (!formData.wardId || !formData.roomId) {
        alert('يرجى اختيار العنبر والغرفة');
        return;
    }

    $.post('/Prisoners/ReturnPrisoner', formData, function(response) {
        if (response.success) {
            alert(response.message);
            $('#returnPrisonerModal').modal('hide');
            location.reload();
        } else {
            alert('خطأ: ' + response.message);
        }
    }).fail(function() {
        alert('حدث خطأ في الاتصال');
    });
}

// إضافة قضية
function addCase(prisonerId) {
    // توجيه إلى صفحة إضافة قضية مع تمرير معرف النزيل
    window.location.href = `/PrisonerCases/Create?prisonerId=${prisonerId}`;
}

// إغلاق النوافذ المنبثقة وإعادة تعيين النماذج
$('.modal').on('hidden.bs.modal', function() {
    $(this).find('form')[0].reset();
    $(this).find('select').val('').trigger('change');
");
            WriteLiteral(@"});
</script>



<style>
    /* Laravel Filament Inspired Design */
    :root {
        --filament-primary: #f59e0b;
        --filament-primary-dark: #d97706;
        --filament-secondary: #6b7280;
        --filament-success: #10b981;
        --filament-warning: #f59e0b;
        --filament-danger: #ef4444;
        --filament-info: #3b82f6;
        --filament-gray-50: #f9fafb;
        --filament-gray-100: #f3f4f6;
        --filament-gray-200: #e5e7eb;
        --filament-gray-300: #d1d5db;
        --filament-gray-400: #9ca3af;
        --filament-gray-500: #6b7280;
        --filament-gray-600: #4b5563;
        --filament-gray-700: #374151;
        --filament-gray-800: #1f2937;
        --filament-gray-900: #111827;
        --filament-white: #ffffff;
        --filament-border-radius: 0.5rem;
        --filament-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --filament-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    * {
        box-sizi");
            WriteLiteral(@"ng: border-box;
    }

    body {
        font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
        background-color: var(--filament-gray-50);
        margin: 0;
        padding: 0;
        color: var(--filament-gray-900);
        line-height: 1.6;
    }

    .filament-layout {
        min-height: 100vh;
        background-color: var(--filament-gray-50);
    }

    /* Header Styles */
    .filament-header {
        background: var(--filament-white);
        border-bottom: 1px solid var(--filament-gray-200);
        padding: 1.5rem 0;
        box-shadow: var(--filament-shadow);
    }

    .header-container {
        max-width: 1280px;
        margin: 0 auto;
        padding: 0 1.5rem;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .header-title-section {
        flex: 1;
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;");
            WriteLiteral(@"
        font-size: 0.875rem;
        color: var(--filament-gray-500);
    }

    .breadcrumb-item {
        color: var(--filament-gray-500);
    }

    .breadcrumb-item.active {
        color: var(--filament-gray-900);
        font-weight: 500;
    }

    .breadcrumb-separator {
        font-size: 0.75rem;
        color: var(--filament-gray-400);
    }

    .page-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0 0 0.5rem 0;
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--filament-gray-900);
    }

    .page-icon {
        color: var(--filament-primary);
        font-size: 1.5rem;
    }

    .page-description {
        margin: 0;
        color: var(--filament-gray-600);
        font-size: 1rem;
    }

    .header-actions {
        display: flex;
        gap: 0.75rem;
        align-items: flex-start;
    }

    /* Button Styles */
    .filament-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    ");
            WriteLiteral(@"    padding: 0.625rem 1rem;
        border: 1px solid transparent;
        border-radius: var(--filament-border-radius);
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        white-space: nowrap;
    }

    .filament-btn-primary {
        background-color: var(--filament-primary);
        color: var(--filament-white);
        border-color: var(--filament-primary);
    }

    .filament-btn-primary:hover {
        background-color: var(--filament-primary-dark);
        border-color: var(--filament-primary-dark);
        color: var(--filament-white);
    }

    .filament-btn-secondary {
        background-color: var(--filament-gray-100);
        color: var(--filament-gray-700);
        border-color: var(--filament-gray-300);
    }

    .filament-btn-secondary:hover {
        background-color: var(--filament-gray-200);
        color: var(--filament-gray-800);
    }

    .filament-btn-outline {
        backg");
            WriteLiteral(@"round-color: transparent;
        color: var(--filament-gray-700);
        border-color: var(--filament-gray-300);
    }

    .filament-btn-outline:hover {
        background-color: var(--filament-gray-50);
        color: var(--filament-gray-800);
    }

    /* Main Content */
    .filament-main {
        padding: 2rem 0;
    }

    .content-container {
        max-width: 1280px;
        margin: 0 auto;
        padding: 0 1.5rem;
    }

    /* Card Styles */
    .filament-card {
        background: var(--filament-white);
        border: 1px solid var(--filament-gray-200);
        border-radius: var(--filament-border-radius);
        box-shadow: var(--filament-shadow);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--filament-gray-200);
        background: var(--filament-gray-50);
    }

    .card-title {
        di");
            WriteLiteral(@"splay: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--filament-gray-900);
    }

    .card-content {
        padding: 1.5rem;
    }

    /* Filters */
    .filters-card .card-content {
        padding: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

    .filters-content {
        padding: 1.5rem;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--filament-gray-700);
        margin: 0;
    }

    .input-wrapper {
        position: relative;
    }

    .input-icon {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform:");
            WriteLiteral(@" translateY(-50%);
        color: var(--filament-gray-400);
        font-size: 0.875rem;
        pointer-events: none;
    }

    .filament-input,
    .filament-select {
        width: 100%;
        padding: 0.625rem 0.75rem;
        padding-right: 2.5rem;
        border: 1px solid var(--filament-gray-300);
        border-radius: var(--filament-border-radius);
        font-size: 0.875rem;
        background-color: var(--filament-white);
        transition: all 0.15s ease-in-out;
    }

    .filament-input:focus,
    .filament-select:focus {
        outline: none;
        border-color: var(--filament-primary);
        box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
    }

    .filters-actions {
        display: flex;
        gap: 0.75rem;
        padding-top: 1rem;
        border-top: 1px solid var(--filament-gray-200);
    }

    .toggle-filters {
        background: none;
        border: none;
        color: var(--filament-gray-500);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.");
            WriteLiteral(@"25rem;
        transition: all 0.15s ease-in-out;
    }

    .toggle-filters:hover {
        background-color: var(--filament-gray-100);
        color: var(--filament-gray-700);
    }

    /* Table Styles */
    .table-card .card-header {
        background: var(--filament-white);
        border-bottom: 1px solid var(--filament-gray-200);
    }

    .table-header-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .records-count {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: var(--filament-gray-600);
        background: var(--filament-gray-100);
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
    }

    .table-header-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .table-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .view-toggle {
        display: flex;
        border: 1px solid var(--filament-");
            WriteLiteral(@"gray-300);
        border-radius: var(--filament-border-radius);
        overflow: hidden;
    }

    .view-btn {
        padding: 0.5rem;
        border: none;
        background: var(--filament-white);
        color: var(--filament-gray-500);
        cursor: pointer;
        transition: all 0.15s ease-in-out;
    }

    .view-btn.active,
    .view-btn:hover {
        background: var(--filament-primary);
        color: var(--filament-white);
    }

    .table-wrapper {
        overflow: hidden;
    }

    .filament-table-container {
        overflow-x: auto;
    }

    .filament-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;
    }

    .filament-table th {
        background: var(--filament-gray-50);
        color: var(--filament-gray-700);
        font-weight: 600;
        padding: 0.75rem 1rem;
        text-align: right;
        border-bottom: 1px solid var(--filament-gray-200);
        white-space: nowrap;
    }

    .filament-table th.sortable {
        curso");
            WriteLiteral(@"r: pointer;
        user-select: none;
    }

    .filament-table th.sortable:hover {
        background: var(--filament-gray-100);
    }

    .th-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
    }

    .sort-icon {
        color: var(--filament-gray-400);
        font-size: 0.75rem;
        transition: color 0.15s ease-in-out;
    }

    .filament-table th.sortable:hover .sort-icon {
        color: var(--filament-gray-600);
    }

    .filament-table td {
        padding: 1rem;
        border-bottom: 1px solid var(--filament-gray-200);
        vertical-align: middle;
    }

    .table-row {
        transition: background-color 0.15s ease-in-out;
    }

    .table-row:hover {
        background: var(--filament-gray-50);
    }

    .table-checkbox {
        width: 3rem;
        text-align: center;
    }

    .filament-checkbox {
        width: 1rem;
        height: 1rem;
        border: 1px solid var(--filament-gray-300);
        bor");
            WriteLiteral(@"der-radius: 0.25rem;
        cursor: pointer;
    }

    .filament-checkbox:checked {
        background-color: var(--filament-primary);
        border-color: var(--filament-primary);
    }

    .actions-header {
        text-align: center;
        width: 5rem;
    }

    /* Table Cell Styles */
    .prisoner-cell {
        min-width: 200px;
    }

    .prisoner-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .prisoner-avatar {
        width: 2.5rem;
        height: 2.5rem;
        background: var(--filament-gray-100);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--filament-gray-500);
        font-size: 1rem;
    }

    .prisoner-details {
        flex: 1;
    }

    .prisoner-name {
        font-weight: 600;
        color: var(--filament-gray-900);
        margin-bottom: 0.25rem;
    }

    .prisoner-number {
        font-size: 0.75rem;
        color: var(--filament-gray-500);
  ");
            WriteLiteral(@"  }

    .national-cell {
        font-family: 'Courier New', monospace;
        font-weight: 500;
    }

    .date-cell {
        text-align: center;
    }

    .date-text {
        display: block;
        font-weight: 500;
        color: var(--filament-gray-900);
    }

    .age-text {
        display: block;
        font-size: 0.75rem;
        color: var(--filament-gray-500);
        margin-top: 0.25rem;
    }

    .nationality-badge {
        background: var(--filament-gray-100);
        color: var(--filament-gray-700);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .charge-info {
        max-width: 200px;
    }

    .charge-text {
        display: block;
        font-weight: 500;
        color: var(--filament-gray-900);
        margin-bottom: 0.25rem;
    }

    .more-cases {
        font-size: 0.75rem;
        color: var(--filament-gray-500);
    }

    /* Filament Badges */
    .filament-badge {
        display: inline-");
            WriteLiteral(@"flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.25rem 0.625rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        white-space: nowrap;
    }

    .filament-badge-primary {
        background: rgba(245, 158, 11, 0.1);
        color: #d97706;
    }

    .filament-badge-success {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
    }

    .filament-badge-warning {
        background: rgba(245, 158, 11, 0.1);
        color: #d97706;
    }

    .filament-badge-danger {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
    }

    .filament-badge-gray {
        background: var(--filament-gray-100);
        color: var(--filament-gray-600);
    }

    .status-dot {
        font-size: 0.5rem;
    }

    /* Actions Dropdown */
    .actions-cell {
        text-align: center;
        position: relative;
    }

    .table-actions-dropdown {
        position: relative;
        display: inline-block;
    }

    .ac");
            WriteLiteral(@"tions-trigger {
        width: 2rem;
        height: 2rem;
        border: none;
        background: transparent;
        color: var(--filament-gray-500);
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .actions-trigger:hover {
        background: var(--filament-gray-100);
        color: var(--filament-gray-700);
    }

    .actions-menu {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: var(--filament-white);
        border: 1px solid var(--filament-gray-200);
        border-radius: var(--filament-border-radius);
        box-shadow: var(--filament-shadow-lg);
        min-width: 10rem;
        z-index: 50;
        opacity: 0;
        visibility: hidden;
        transition: all 0.15s ease-in-out;
        margin-top: 0.25rem;
    }

    .actions-menu.show {
        opacity: 1;
        visibili");
            WriteLiteral(@"ty: visible;
    }

    .action-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        color: var(--filament-gray-700);
        text-decoration: none;
        font-size: 0.875rem;
        border: none;
        background: none;
        width: 100%;
        text-align: right;
        cursor: pointer;
        transition: background-color 0.15s ease-in-out;
    }

    .action-item:hover {
        background: var(--filament-gray-50);
        color: var(--filament-gray-900);
    }

    .action-item.danger {
        color: var(--filament-danger);
    }

    .action-item.danger:hover {
        background: rgba(239, 68, 68, 0.05);
        color: var(--filament-danger);
    }

    .action-divider {
        height: 1px;
        background: var(--filament-gray-200);
        margin: 0.25rem 0;
    }

    /* Pagination */
    .table-pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem");
            WriteLiteral(@" 1.5rem;
        border-top: 1px solid var(--filament-gray-200);
        background: var(--filament-gray-50);
    }

    .pagination-info {
        font-size: 0.875rem;
        color: var(--filament-gray-600);
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .pagination-btn {
        width: 2rem;
        height: 2rem;
        border: 1px solid var(--filament-gray-300);
        background: var(--filament-white);
        color: var(--filament-gray-500);
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pagination-btn:not(:disabled):hover {
        background: var(--filament-gray-50);
        color: var(--filament-gray-700);
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-numbers {
        display: flex;
        gap: 0.25rem;
        mar");
            WriteLiteral(@"gin: 0 0.5rem;
    }

    .pagination-number {
        width: 2rem;
        height: 2rem;
        border: 1px solid var(--filament-gray-300);
        background: var(--filament-white);
        color: var(--filament-gray-700);
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-number.active {
        background: var(--filament-primary);
        border-color: var(--filament-primary);
        color: var(--filament-white);
    }

    .pagination-number:not(.active):hover {
        background: var(--filament-gray-50);
    }

    /* Responsive Design */
    ");
            WriteLiteral(@"@media (max-width: 1024px) {
        .header-content {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .filters-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .table-header-right {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }
    }

    ");
            WriteLiteral(@"@media (max-width: 768px) {
        .content-container {
            padding: 0 1rem;
        }

        .filters-grid {
            grid-template-columns: 1fr;
        }

        .filters-actions {
            flex-direction: column;
        }

        .table-header-left {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .table-actions {
            flex-wrap: wrap;
        }

        .filament-table {
            font-size: 0.75rem;
        }

        .filament-table th,
        .filament-table td {
            padding: 0.5rem;
        }

        .prisoner-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .prisoner-avatar {
            width: 2rem;
            height: 2rem;
        }

        .table-pagination {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
    }

    ");
            WriteLiteral(@"@media (max-width: 640px) {
        .filament-main {
            padding: 1rem 0;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .breadcrumb {
            font-size: 0.75rem;
        }

        .card-header {
            padding: 1rem;
        }

        .card-content {
            padding: 1rem;
        }

        .filters-content {
            padding: 1rem;
        }
    }

    /* Print Styles */
    ");
            WriteLiteral(@"@media print {
        .filament-header,
        .filters-card,
        .table-actions,
        .actions-cell,
        .table-pagination {
            display: none !important;
        }

        .filament-layout {
            background: white !important;
        }

        .filament-card {
            box-shadow: none !important;
            border: none !important;
        }

        .filament-table {
            font-size: 0.75rem;
        }

        .filament-table th,
        .filament-table td {
            padding: 0.25rem !important;
            border: 1px solid #000 !important;
        }
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    ");
            WriteLiteral(@"@keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .slide-down {
        animation: slideDown 0.3s ease-in-out;
    }

    ");
            WriteLiteral(@"@keyframes slideDown {
        from {
            opacity: 0;
            max-height: 0;
        }
        to {
            opacity: 1;
            max-height: 500px;
        }
    }
</style>

<script>
    // Filament-style JavaScript functionality
    document.addEventListener('DOMContentLoaded', function() {
        initializeFilters();
        initializeTable();
        initializeActions();
    });

    function initializeFilters() {
        // Toggle filters visibility
        const toggleBtn = document.querySelector('.toggle-filters');
        const filtersContent = document.getElementById('filtersContent');

        if (toggleBtn && filtersContent) {
            toggleBtn.addEventListener('click', function() {
                const isVisible = filtersContent.style.display !== 'none';
                filtersContent.style.display = isVisible ? 'none' : 'block';

                const icon = toggleBtn.querySelector('i');
                icon.className = isVisible ? 'fas fa-chevron-down' : 'fas fa-chevron-u");
            WriteLiteral(@"p';
            });
        }

        // Real-time search
        const searchInputs = document.querySelectorAll('.filament-input, .filament-select');
        searchInputs.forEach(input => {
            input.addEventListener('input', debounce(searchPrisoners, 300));
        });
    }

    function initializeTable() {
        // Select all checkbox functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });
        }

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
                se");
            WriteLiteral(@"lectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
                updateBulkActions();
            });
        });

        // Sortable columns
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const sortField = this.dataset.sort;
                sortTable(sortField);
            });
        });
    }

    function initializeActions() {
        // Close actions menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.table-actions-dropdown')) {
                document.querySelectorAll('.actions-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
    }

    function toggleActionsMenu(trigger) {
        const menu = trigge");
            WriteLiteral(@"r.nextElementSibling;
        const isVisible = menu.classList.contains('show');

        // Close all other menus
        document.querySelectorAll('.actions-menu').forEach(m => {
            m.classList.remove('show');
        });

        // Toggle current menu
        if (!isVisible) {
            menu.classList.add('show');
        }
    }

    function searchPrisoners() {
        const searchName = document.getElementById('searchName').value.toLowerCase();
        const searchNational = document.getElementById('searchNational').value.toLowerCase();
        const searchCase = document.getElementById('searchCase').value.toLowerCase();
        const filterWard = document.getElementById('filterWard').value;
        const filterStatus = document.getElementById('filterStatus').value;
        const filterNationality = document.getElementById('filterNationality').value;

        const rows = document.querySelectorAll('#prisonersTableBody .table-row');
        let visibleCount = 0;

        rows.forEach(row => {");
            WriteLiteral(@"
            let show = true;

            const name = row.querySelector('.prisoner-name').textContent.toLowerCase();
            const national = row.querySelector('.national-cell').textContent.toLowerCase();
            const nationality = row.querySelector('.nationality-badge').textContent.toLowerCase();

            if (searchName && !name.includes(searchName)) show = false;
            if (searchNational && !national.includes(searchNational)) show = false;
            if (filterNationality && !nationality.includes(filterNationality.toLowerCase())) show = false;

            if (show) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        document.getElementById('totalResults').textContent = visibleCount;
    }

    function clearSearch() {
        document.querySelectorAll('.filament-input, .filament-select').forEach(input => {
            input.value = '';
        });

        document");
            WriteLiteral(".querySelectorAll(\'#prisonersTableBody .table-row\').forEach(row => {\n            row.style.display = \'\';\n        });\n\n        document.getElementById(\'totalResults\').textContent = \'");
            Write(
#nullable restore
#line 2016 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Prisoners\Control.cshtml"
                                                                Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"';
    }

    function sortTable(field) {
        // Implement sorting logic here
        console.log('Sorting by:', field);
    }

    function updateBulkActions() {
        const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
        // Update bulk actions UI based on selection
        console.log('Selected items:', checkedCount);
    }

    function exportToExcel() {
        // Implement Excel export
        alert('تصدير إلى Excel - سيتم تطوير هذه الميزة قريباً');
    }

    function printResults() {
        window.print();
    }

    function movePrisoner(id) {
        alert('نقل النزيل - سيتم تطوير هذه الميزة قريباً');
    }

    function deletePrisoner(id) {
        if (confirm('هل أنت متأكد من حذف هذا النزيل؟')) {
            alert('حذف النزيل - سيتم تطوير هذه الميزة قريباً');
        }
    }

    function toggleFilters() {
        const filtersContent = document.getElementById('filtersContent');
        const toggleBtn = document.querySelector('.toggle-filters i');

        ");
            WriteLiteral(@"if (filtersContent.style.display === 'none') {
            filtersContent.style.display = 'block';
            toggleBtn.className = 'fas fa-chevron-up';
        } else {
            filtersContent.style.display = 'none';
            toggleBtn.className = 'fas fa-chevron-down';
        }
    }

    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
</script>
</style>

");
            DefineSection("Scripts", async() => {
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bc4fedcaf9df992111d581d24e59ec91f7e43de2cf87f8002ebaecd5020043c1137524", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n");
            }
            );
            WriteLiteral("\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<IEnumerable<PrisonManagementSystem.Models.Prisoner>> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
