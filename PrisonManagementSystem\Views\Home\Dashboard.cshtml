@{
    ViewData["Title"] = "لوحة التحكم";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<!-- Dashboard Stats -->
<div class="filament-stats">
    <div class="filament-stat-card">
        <div class="filament-stat-icon primary">
            <i class="fas fa-users"></i>
        </div>
        <div class="filament-stat-value" id="totalPrisoners">0</div>
        <div class="filament-stat-label">إجمالي النزلاء</div>
        <div style="font-size: 12px; color: var(--gray-500); margin-top: 4px;">
            <i class="fas fa-arrow-up" style="color: var(--success-500);"></i>
            +5 هذا الأسبوع
        </div>
    </div>
    
    <div class="filament-stat-card">
        <div class="filament-stat-icon success">
            <i class="fas fa-building"></i>
        </div>
        <div class="filament-stat-value">3</div>
        <div class="filament-stat-label">العنابر النشطة</div>
        <div style="font-size: 12px; color: var(--gray-500); margin-top: 4px;">
            <i class="fas fa-check-circle" style="color: var(--success-500);"></i>
            جميعها متاحة
        </div>
    </div>
    
    <div class="filament-stat-card">
        <div class="filament-stat-icon warning">
            <i class="fas fa-door-open"></i>
        </div>
        <div class="filament-stat-value" id="availableRooms">0</div>
        <div class="filament-stat-label">الغرف المتاحة</div>
        <div style="font-size: 12px; color: var(--gray-500); margin-top: 4px;">
            <i class="fas fa-info-circle" style="color: var(--warning-500);"></i>
            من أصل <span id="totalRooms">0</span> غرفة
        </div>
    </div>
    
    <div class="filament-stat-card">
        <div class="filament-stat-icon danger">
            <i class="fas fa-calendar-check"></i>
        </div>
        <div class="filament-stat-value" id="releaseDue">0</div>
        <div class="filament-stat-label">مستحقو الإفراج</div>
        <div style="font-size: 12px; color: var(--gray-500); margin-top: 4px;">
            <i class="fas fa-clock" style="color: var(--danger-500);"></i>
            خلال 30 يوم
        </div>
    </div>
</div>

<!-- Charts Row -->
<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 24px; margin-bottom: 32px;">
    <!-- Prisoners by Ward Chart -->
    <div class="filament-card">
        <div class="filament-card-header">
            <h3 class="filament-card-title">
                <i class="fas fa-chart-bar"></i>
                توزيع النزلاء حسب العنابر
            </h3>
        </div>
        <div class="filament-card-content">
            <canvas id="wardChart" width="400" height="200"></canvas>
        </div>
    </div>
    
    <!-- Status Distribution -->
    <div class="filament-card">
        <div class="filament-card-header">
            <h3 class="filament-card-title">
                <i class="fas fa-chart-pie"></i>
                توزيع الحالات
            </h3>
        </div>
        <div class="filament-card-content">
            <canvas id="statusChart" width="300" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Recent Activities and Alerts -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 32px;">
    <!-- Recent Activities -->
    <div class="filament-card">
        <div class="filament-card-header">
            <h3 class="filament-card-title">
                <i class="fas fa-clock"></i>
                الأنشطة الأخيرة
            </h3>
        </div>
        <div class="filament-card-content">
            <div style="display: flex; flex-direction: column; gap: 16px;">
                <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                    <div style="width: 40px; height: 40px; background: var(--primary-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-user-plus" style="color: var(--primary-600);"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: var(--gray-900);">تم إضافة نزيل جديد</div>
                        <div style="font-size: 14px; color: var(--gray-600);">أحمد محمد العلي - العنبر الأول</div>
                    </div>
                    <div style="font-size: 12px; color: var(--gray-500);">منذ ساعتين</div>
                </div>
                
                <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                    <div style="width: 40px; height: 40px; background: var(--success-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-gavel" style="color: var(--success-500);"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: var(--gray-900);">تم تحديث قضية</div>
                        <div style="font-size: 14px; color: var(--gray-600);">قضية رقم C001 - تم تحديد موعد المحاكمة</div>
                    </div>
                    <div style="font-size: 12px; color: var(--gray-500);">منذ 4 ساعات</div>
                </div>
                
                <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                    <div style="width: 40px; height: 40px; background: var(--warning-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-exchange-alt" style="color: var(--warning-500);"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: var(--gray-900);">حركة نقل</div>
                        <div style="font-size: 14px; color: var(--gray-600);">نقل نزيل من العنبر الثاني إلى العنبر الأول</div>
                    </div>
                    <div style="font-size: 12px; color: var(--gray-500);">أمس</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Alerts and Notifications -->
    <div class="filament-card">
        <div class="filament-card-header">
            <h3 class="filament-card-title">
                <i class="fas fa-bell"></i>
                التنبيهات والإشعارات
            </h3>
        </div>
        <div class="filament-card-content">
            <div style="display: flex; flex-direction: column; gap: 12px;">
                <div class="filament-alert warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>تنبيه:</strong> 3 نزلاء مستحقون للإفراج خلال الأسبوع القادم
                    </div>
                </div>
                
                <div class="filament-alert info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>معلومة:</strong> العنبر الثالث يحتاج إلى صيانة دورية
                    </div>
                </div>
                
                <div class="filament-alert success">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <strong>تم:</strong> تحديث بيانات 15 نزيل بنجاح
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="filament-card">
    <div class="filament-card-header">
        <h3 class="filament-card-title">
            <i class="fas fa-bolt"></i>
            الإجراءات السريعة
        </h3>
    </div>
    <div class="filament-card-content">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
            <a href="@Url.Action("Create", "Prisoners")" class="filament-btn filament-btn-primary filament-btn-lg">
                <i class="fas fa-user-plus"></i>
                إضافة نزيل جديد
            </a>
            <a href="@Url.Action("Control", "Prisoners")" class="filament-btn filament-btn-secondary filament-btn-lg">
                <i class="fas fa-list-alt"></i>
                كنترول النزلاء
            </a>
            <a href="@Url.Action("Index", "PrisonerMovements")" class="filament-btn filament-btn-secondary filament-btn-lg">
                <i class="fas fa-exchange-alt"></i>
                حركات النقل
            </a>
            <a href="@Url.Action("Index", "Reports")" class="filament-btn filament-btn-secondary filament-btn-lg">
                <i class="fas fa-chart-line"></i>
                التقارير
            </a>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load dashboard data
            loadDashboardData();
            
            // Initialize charts
            initializeCharts();
            
            // Update data every 30 seconds
            setInterval(loadDashboardData, 30000);
        });
        
        function loadDashboardData() {
            // This would typically fetch data from an API
            // For now, we'll use mock data
            document.getElementById('totalPrisoners').textContent = '125';
            document.getElementById('availableRooms').textContent = '18';
            document.getElementById('totalRooms').textContent = '45';
            document.getElementById('releaseDue').textContent = '7';
        }
        
        function initializeCharts() {
            // Ward distribution chart
            const wardCtx = document.getElementById('wardChart').getContext('2d');
            new Chart(wardCtx, {
                type: 'bar',
                data: {
                    labels: ['العنبر الأول', 'العنبر الثاني', 'العنبر الثالث'],
                    datasets: [{
                        label: 'عدد النزلاء',
                        data: [45, 38, 42],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ],
                        borderColor: [
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)',
                            'rgb(245, 158, 11)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Status distribution chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['محكوم', 'موقوف', 'مبرأ'],
                    datasets: [{
                        data: [85, 30, 10],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ],
                        borderColor: [
                            'rgb(239, 68, 68)',
                            'rgb(245, 158, 11)',
                            'rgb(16, 185, 129)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    </script>
}
