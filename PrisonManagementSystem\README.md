# نظام إدارة سجن الكويفية 🛡️
## PRISON MANAGEMENT SYSTEM

### نظرة عامة
نظام شامل وعصري لإدارة سجن الكويفية مطور باستخدام ASP.NET Core 5.0 مع قاعدة بيانات SQL Server محلية.
يتميز النظام بتصميم عسكري أنيق مع ألوان خضراء مموهة وواجهات تفاعلية متطورة.

## المتطلبات
- .NET 5.0 SDK
- SQL Server LocalDB أو SQL Server Express
- متصفح ويب حديث

## 🎨 التصميم العسكري المتطور

### خصائص التصميم:
- **ألوان عسكرية**: نظام ألوان أخضر مموه مع لمسات ذهبية
- **تأثيرات متحركة**: خلفيات متدرجة متحركة وتأثيرات إضاءة
- **واجهات تفاعلية**: بطاقات ثلاثية الأبعاد مع تأثيرات الحركة
- **أيقونات عسكرية**: رموز وشارات عسكرية احترافية
- **تصميم متجاوب**: يعمل على جميع الأجهزة والشاشات

## 🔧 الميزات المطورة

### 1. 🔐 نظام المصادقة والتفويض العسكري
- واجهة تسجيل دخول عسكرية أنيقة مع شعار وتأثيرات
- نظام أدوار وصلاحيات متقدم
- حماية متعددة المستويات للصفحات
- تتبع جلسات المستخدمين وأنشطتهم

### 2. إدارة النزلاء
- إضافة سجين جديد مع البيانات الكاملة:
  - البيانات الشخصية (الاسم، تاريخ الميلاد، الجنسية، إلخ)
  - البيانات الصحية (الحالة الصحية، الأمراض المزمنة، نسبة العجز)
  - بيانات السجن (تاريخ الدخول، العنبر، الغرفة)
- تعديل بيانات النزلاء
- عرض تفاصيل السجين
- توليد رقم السجين تلقائياً

### 3. إدارة العنابر والغرف
- إضافة وإدارة العنابر
- تتبع السعة والإشغال
- إحصائيات مفصلة

### 4. واجهة كنترول النزلاء
- جدول تفاعلي لعرض جميع النزلاء
- أدوات بحث متقدمة:
  - البحث بالاسم
  - البحث بالرقم الوطني
  - البحث برقم القضية
  - فلترة بالجنسية
  - فلترة بالعنبر والغرفة
- إجراءات سريعة لكل نزيل

### 5. الواجهة الرئيسية
- لوحة تحكم شاملة
- قوائم منسدلة منظمة
- إحصائيات سريعة
- تصميم عربي متجاوب

## بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** Admin123!

## كيفية التشغيل

### 1. إعداد قاعدة البيانات
```bash
dotnet ef database update
```

### 2. تشغيل التطبيق
```bash
dotnet run
```

### 3. فتح المتصفح
انتقل إلى: `https://localhost:5001`

## هيكل قاعدة البيانات

### الجداول الرئيسية:
- **Prisoners**: بيانات النزلاء
- **Wards**: العنابر
- **Rooms**: الغرف
- **Shilas**: الشيل
- **PrisonerCases**: قضايا النزلاء
- **PrisonerMovements**: حركات النزلاء
- **MovementTypes**: أنواع الحركات
- **AspNetUsers**: المستخدمين
- **AspNetRoles**: الأدوار

## الميزات المخطط تطويرها

### المرحلة التالية:
1. **نظام القضايا**
   - إضافة قضايا متعددة للسجين الواحد
   - حساب مدة الحكم الإجمالية
   - تتبع تواريخ انتهاء الأحكام

2. **نظام الحركات**
   - النقل الخارجي (إفراج، تشغيل خارجي، مأمورية)
   - النقل الداخلي بين العنابر والغرف
   - تسجيل تاريخ الحركات

3. **نظام التقارير**
   - تقارير مفصلة عن النزلاء
   - تقارير الحركات
   - إحصائيات شاملة

4. **بطاقة السجين**
   - عرض شامل لبيانات السجين
   - طباعة البطاقة
   - تاريخ القضايا والأحكام

5. **إدارة الغرف والشيل**
   - واجهات إدارة كاملة
   - تتبع السعة والإشغال

## التقنيات المستخدمة
- **Backend**: ASP.NET Core 5.0 MVC
- **Database**: Entity Framework Core مع SQL Server
- **Frontend**: Bootstrap 4, jQuery, Font Awesome
- **Authentication**: ASP.NET Core Identity
- **UI**: تصميم عربي متجاوب (RTL)

## الأمان
- تشفير كلمات المرور
- حماية من CSRF
- تفويض على مستوى Controller
- تسجيل العمليات مع اسم المستخدم والتاريخ

## الدعم والتطوير
هذا النظام قابل للتوسع والتطوير حسب احتياجات السجن. يمكن إضافة المزيد من الميزات والتقارير حسب المتطلبات.
