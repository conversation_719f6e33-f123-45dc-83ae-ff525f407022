using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PrisonManagementSystem.Models
{
    public class Room
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم الغرفة")]
        public string RoomNumber { get; set; }

        [StringLength(200)]
        [Display(Name = "الوصف")]
        public string Description { get; set; }

        [Display(Name = "السعة القصوى")]
        public int MaxCapacity { get; set; }

        [Display(Name = "العدد الحالي")]
        public int CurrentCount { get; set; }

        [Display(Name = "نشطة")]
        public bool IsActive { get; set; } = true;

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع الغرفة")]
        public string RoomType { get; set; } = "عادية"; // عادية، شيلة، عزل، طبية

        // العلاقة مع العنبر
        [Required]
        [Display(Name = "العنبر")]
        public int WardId { get; set; }
        [ForeignKey("WardId")]
        public virtual Ward Ward { get; set; }

        // العلاقات
        public virtual ICollection<Prisoner> Prisoners { get; set; } = new List<Prisoner>();
        public virtual ICollection<Shila> Shilas { get; set; } = new List<Shila>();
    }
}
