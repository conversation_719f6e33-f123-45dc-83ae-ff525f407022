using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PrisonManagementSystem.Models
{
    public class PrisonerCase
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم القضية")]
        public string CaseNumber { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "نوع القضية")]
        public string CaseType { get; set; }

        [StringLength(500)]
        [Display(Name = "وصف القضية")]
        public string CaseDescription { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "التهمة")]
        public string Charge { get; set; }

        [StringLength(500)]
        [Display(Name = "تفاصيل التهمة")]
        public string ChargeDetails { get; set; }

        [StringLength(100)]
        [Display(Name = "الحكم")]
        public string Verdict { get; set; }

        [Display(Name = "مدة الحكم (بالأيام)")]
        public int? SentenceDays { get; set; }

        [Display(Name = "الغرامة")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Fine { get; set; }

        [Display(Name = "تاريخ بداية الحكم")]
        public DateTime? SentenceStartDate { get; set; }

        [Display(Name = "تاريخ انتهاء الحكم")]
        public DateTime? SentenceEndDate { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "حالة الحكم")]
        public string VerdictStatus { get; set; } // محكوم، موقوف، مبرأ

        [Display(Name = "تاريخ القضية")]
        public DateTime CaseDate { get; set; }

        [StringLength(100)]
        [Display(Name = "المحكمة")]
        public string Court { get; set; }

        [StringLength(100)]
        [Display(Name = "اسم المحكمة")]
        public string CourtName { get; set; }

        [StringLength(100)]
        [Display(Name = "القاضي")]
        public string Judge { get; set; }

        [StringLength(100)]
        [Display(Name = "اسم القاضي")]
        public string JudgeName { get; set; }

        [StringLength(100)]
        [Display(Name = "نوع الحكم")]
        public string SentenceType { get; set; }

        [StringLength(100)]
        [Display(Name = "مدة الحكم")]
        public string SentenceDuration { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string Notes { get; set; }

        [Display(Name = "نشطة")]
        public bool IsActive { get; set; } = true;

        // العلاقة مع السجين
        [Required]
        [Display(Name = "السجين")]
        public int PrisonerId { get; set; }
        [ForeignKey("PrisonerId")]
        public virtual Prisoner Prisoner { get; set; }

        // تواريخ النظام
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedDate { get; set; }

        [StringLength(50)]
        [Display(Name = "المستخدم المنشئ")]
        public string CreatedBy { get; set; }

        [StringLength(50)]
        [Display(Name = "المستخدم المحدث")]
        public string UpdatedBy { get; set; }
    }
}
