/* Professional Effects & Animations for Enhanced UI */

/* Advanced CSS Variables */
:root {
    --animation-duration-fast: 0.15s;
    --animation-duration-normal: 0.3s;
    --animation-duration-slow: 0.5s;
    --animation-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --animation-timing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    --blur-sm: blur(4px);
    --blur-md: blur(8px);
    --blur-lg: blur(16px);
    
    --backdrop-blur: blur(10px);
    --backdrop-saturate: saturate(180%);
    
    --glow-primary: 0 0 20px rgba(99, 102, 241, 0.3);
    --glow-success: 0 0 20px rgba(16, 185, 129, 0.3);
    --glow-warning: 0 0 20px rgba(245, 158, 11, 0.3);
    --glow-danger: 0 0 20px rgba(239, 68, 68, 0.3);
}

/* Enhanced Scroll Behavior */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 2rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #cbd5e1, #94a3b8);
    border-radius: 4px;
    transition: background var(--animation-duration-normal) var(--animation-timing);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #94a3b8, #64748b);
}

/* Enhanced Focus States */
*:focus {
    outline: none;
}

.focus-ring:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    border-color: #6366f1;
}

/* Advanced Loading States */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.loading-spin {
    animation: spin 1s linear infinite;
}

/* Enhanced Hover Effects */
.hover-lift {
    transition: transform var(--animation-duration-normal) var(--animation-timing);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-scale {
    transition: transform var(--animation-duration-normal) var(--animation-timing);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--animation-duration-normal) var(--animation-timing);
}

.hover-glow:hover {
    box-shadow: var(--glow-primary);
}

/* Enhanced Button Effects */
.btn-ripple {
    position: relative;
    overflow: hidden;
}

.btn-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width var(--animation-duration-slow) var(--animation-timing),
                height var(--animation-duration-slow) var(--animation-timing);
}

.btn-ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Enhanced Card Effects */
.card-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-gradient {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: var(--backdrop-blur);
}

.card-shadow-hover {
    transition: box-shadow var(--animation-duration-normal) var(--animation-timing);
}

.card-shadow-hover:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Enhanced Text Effects */
.text-gradient {
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-glow {
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

/* Enhanced Form Effects */
.form-floating-enhanced {
    position: relative;
}

.form-floating-enhanced input,
.form-floating-enhanced textarea {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    transition: all var(--animation-duration-normal) var(--animation-timing);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: var(--backdrop-blur);
}

.form-floating-enhanced input:focus,
.form-floating-enhanced textarea:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
}

.form-floating-enhanced label {
    position: absolute;
    top: 1rem;
    right: 1.25rem;
    font-size: 1rem;
    color: #64748b;
    pointer-events: none;
    transition: all var(--animation-duration-normal) var(--animation-timing);
    background: white;
    padding: 0 0.5rem;
}

.form-floating-enhanced input:focus + label,
.form-floating-enhanced input:not(:placeholder-shown) + label,
.form-floating-enhanced textarea:focus + label,
.form-floating-enhanced textarea:not(:placeholder-shown) + label {
    top: -0.5rem;
    font-size: 0.875rem;
    color: #6366f1;
    font-weight: 600;
}

/* Enhanced Table Effects */
.table-enhanced {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 2px solid #e2e8f0;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    color: #374151;
}

.table-enhanced tbody tr {
    transition: all var(--animation-duration-normal) var(--animation-timing);
}

.table-enhanced tbody tr:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
    transform: scale(1.001);
}

/* Enhanced Modal Effects */
.modal-enhanced .modal-dialog {
    transform: scale(0.8);
    transition: transform var(--animation-duration-normal) var(--animation-timing);
}

.modal-enhanced.show .modal-dialog {
    transform: scale(1);
}

.modal-enhanced .modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    backdrop-filter: var(--backdrop-blur);
}

/* Enhanced Alert Effects */
.alert-enhanced {
    border: none;
    border-radius: 12px;
    border-left: 4px solid;
    backdrop-filter: var(--backdrop-blur);
    position: relative;
    overflow: hidden;
}

.alert-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    opacity: 0.3;
}

.alert-enhanced.alert-primary {
    background: rgba(99, 102, 241, 0.1);
    border-left-color: #6366f1;
    color: #4338ca;
}

.alert-enhanced.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: #10b981;
    color: #047857;
}

.alert-enhanced.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
    color: #92400e;
}

.alert-enhanced.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
    color: #b91c1c;
}

/* Enhanced Badge Effects */
.badge-enhanced {
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    overflow: hidden;
}

.badge-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--animation-duration-slow) var(--animation-timing);
}

.badge-enhanced:hover::before {
    left: 100%;
}

/* Enhanced Navigation Effects */
.nav-enhanced .nav-link {
    position: relative;
    transition: all var(--animation-duration-normal) var(--animation-timing);
    border-radius: 8px;
    margin: 0.25rem;
}

.nav-enhanced .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #6366f1;
    transition: all var(--animation-duration-normal) var(--animation-timing);
    transform: translateX(-50%);
}

.nav-enhanced .nav-link:hover::before,
.nav-enhanced .nav-link.active::before {
    width: 80%;
}

/* Enhanced Dropdown Effects */
.dropdown-enhanced .dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: var(--backdrop-blur);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-10px);
    opacity: 0;
    transition: all var(--animation-duration-normal) var(--animation-timing);
}

.dropdown-enhanced.show .dropdown-menu {
    transform: translateY(0);
    opacity: 1;
}

.dropdown-enhanced .dropdown-item {
    border-radius: 8px;
    margin: 0.25rem;
    transition: all var(--animation-duration-normal) var(--animation-timing);
}

.dropdown-enhanced .dropdown-item:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    transform: translateX(-4px);
}

/* Enhanced Progress Effects */
.progress-enhanced {
    height: 8px;
    border-radius: 4px;
    background: #e2e8f0;
    overflow: hidden;
    position: relative;
}

.progress-enhanced .progress-bar {
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.progress-enhanced .progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .hover-lift:hover,
    .hover-scale:hover {
        transform: none;
    }
    
    .card-shadow-hover:hover {
        box-shadow: inherit;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
