@model PrisonManagementSystem.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة سجن الكويفية</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/filament-theme.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            border: 1px solid var(--gray-200);
        }

        .login-header {
            background: linear-gradient(135deg, var(--military-gold) 0%, #B8860B 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            position: relative;
            z-index: 1;
        }

        .login-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .login-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .login-badge {
            font-size: 11px;
            opacity: 0.8;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .login-body {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .form-control {
            padding: 14px 16px;
            border: 2px solid var(--gray-200);
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .form-control:focus {
            border-color: var(--military-gold);
            box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
            outline: none;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--military-gold) 0%, #B8860B 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        .form-check {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            accent-color: var(--military-gold);
        }

        .login-footer {
            background: var(--gray-50);
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid var(--gray-200);
        }

        .footer-text {
            font-size: 12px;
            color: var(--gray-600);
            margin: 0;
        }

        .text-danger {
            color: #ef4444 !important;
            font-size: 12px;
            margin-top: 4px;
            display: block;
        }

        .validation-summary-errors {
            background: #fee2e2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
        }

        .validation-summary-errors ul {
            margin: 0;
            padding-right: 20px;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="~/images/logo.svg" alt="شعار القوات المسلحة العراقية" class="login-logo">
                <h2 class="login-title">تسجيل الدخول</h2>
                <p class="login-subtitle">نظام إدارة سجن الكويفية</p>
                <p class="login-badge">الكتيبة 210 مشاة آلية</p>
            </div>

            <div class="login-body">
                <form asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post">
                    <div asp-validation-summary="All" class="validation-summary-errors"></div>

                    <div class="form-group">
                        <label asp-for="UserName" class="form-label">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input asp-for="UserName" class="form-control" placeholder="أدخل اسم المستخدم" autocomplete="username" />
                        <span asp-validation-for="UserName" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Password" class="form-label">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <input asp-for="Password" class="form-control" placeholder="أدخل كلمة المرور" autocomplete="current-password" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>

                    <div class="form-check">
                        <input asp-for="RememberMe" class="form-check-input" type="checkbox" />
                        <label asp-for="RememberMe" class="form-check-label">تذكرني</label>
                    </div>

                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        دخول النظام
                    </button>
                </form>
            </div>

            <div class="login-footer">
                <p class="footer-text">القوات المسلحة العراقية - جميع الحقوق محفوظة © 2024</p>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js"></script>
</body>
</html>





@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
