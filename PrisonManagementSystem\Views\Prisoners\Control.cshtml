@model IEnumerable<PrisonManagementSystem.Models.Prisoner>

@{
    ViewData["Title"] = "كنترول النزلاء";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<!-- Page Header -->
<div class="filament-page-header">
    <div>
        <nav class="filament-breadcrumbs">
            <a href="@Url.Action("Index", "Home")" class="filament-breadcrumb">الرئيسية</a>
            <span class="filament-breadcrumb-separator"><i class="fas fa-chevron-left"></i></span>
            <span class="filament-breadcrumb active">كنترول النزلاء</span>
        </nav>
        <h1 class="filament-page-title">
            <i class="fas fa-users"></i>
            كنترول النزلاء
        </h1>
    </div>
    <div class="filament-page-actions">
        <a href="@Url.Action("Create", "Prisoners")" class="filament-btn filament-btn-primary">
            <i class="fas fa-plus"></i>
            إضافة نزيل جديد
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="filament-search-bar">
    <div class="filament-search-row">
        <div class="filament-search-field">
            <label>البحث العام</label>
            <input type="text" id="globalSearch" placeholder="البحث بالاسم، الرقم الوطني، رقم السجين..." class="form-control">
        </div>
        <div class="filament-search-field">
            <label>العنبر</label>
            <select id="wardFilter" class="form-control">
                <option value="">جميع العنابر</option>
                <option value="1">العنبر الأول</option>
                <option value="2">العنبر الثاني</option>
                <option value="3">العنبر الثالث</option>
            </select>
        </div>
        <div class="filament-search-field">
            <label>الحالة</label>
            <select id="statusFilter" class="form-control">
                <option value="">جميع الحالات</option>
                <option value="محكوم">محكوم</option>
                <option value="موقوف">موقوف</option>
                <option value="مبرأ">مبرأ</option>
            </select>
        </div>
        <div class="filament-search-actions">
            <button type="button" class="filament-btn filament-btn-primary" onclick="applyFilters()">
                <i class="fas fa-search"></i>
                بحث
            </button>
            <button type="button" class="filament-btn filament-btn-secondary" onclick="clearFilters()">
                <i class="fas fa-times"></i>
                مسح
            </button>
        </div>
    </div>
</div>
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label class="filter-label">البحث بالاسم</label>
                            <div class="input-wrapper">
                                <i class="fas fa-search input-icon"></i>
                                <input type="text" id="searchName" class="filament-input" placeholder="اكتب اسم النزيل...">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">الرقم الوطني</label>
                            <div class="input-wrapper">
                                <i class="fas fa-id-card input-icon"></i>
                                <input type="text" id="searchNational" class="filament-input" placeholder="الرقم الوطني...">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">رقم القضية</label>
                            <div class="input-wrapper">
                                <i class="fas fa-gavel input-icon"></i>
                                <input type="text" id="searchCase" class="filament-input" placeholder="رقم القضية...">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">العنبر</label>
                            <select id="filterWard" class="filament-select">
                                <option value="">جميع العنابر</option>
                                <option value="1">العنبر الأول</option>
                                <option value="2">العنبر الثاني</option>
                                <option value="3">العنبر الثالث</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">الحالة</label>
                            <select id="filterStatus" class="filament-select">
                                <option value="">جميع الحالات</option>
                                <option value="محبوس">محبوس</option>
                                <option value="موقوف">موقوف</option>
                                <option value="مبرأ">مبرأ</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">الجنسية</label>
                            <select id="filterNationality" class="filament-select">
                                <option value="">جميع الجنسيات</option>
                                <option value="عراقي">عراقي</option>
                                <option value="سوري">سوري</option>
                                <option value="أردني">أردني</option>
                            </select>
                        </div>
                    </div>
                    <div class="filters-actions">
                        <button type="button" class="filament-btn filament-btn-primary" onclick="searchPrisoners()">
                            <i class="fas fa-search"></i>
                            تطبيق الفلاتر
                        </button>
                        <button type="button" class="filament-btn filament-btn-secondary" onclick="clearSearch()">
                            <i class="fas fa-refresh"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>

            <!-- Data Table Card -->
            <div class="filament-card table-card">
                <div class="card-header">
                    <div class="table-header-left">
                        <h3 class="card-title">
                            <i class="fas fa-table"></i>
                            قائمة النزلاء
                        </h3>
                        <span class="records-count">
                            <i class="fas fa-users"></i>
                            إجمالي <strong id="totalResults">@Model.Count()</strong> نزيل
                        </span>
                    </div>
                    <div class="table-header-right">
                        <div class="table-actions">
                            <button class="filament-btn filament-btn-outline" onclick="exportToExcel()">
                                <i class="fas fa-download"></i>
                                تصدير
                            </button>
                            <button class="filament-btn filament-btn-outline" onclick="printResults()">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                            <div class="view-toggle">
                                <button class="view-btn active" data-view="table">
                                    <i class="fas fa-table"></i>
                                </button>
                                <button class="view-btn" data-view="grid">
                                    <i class="fas fa-th-large"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-wrapper">
                    <div class="filament-table-container">
                        <table class="filament-table" id="prisonersTable">
                            <thead>
                                <tr>
                                    <th class="table-checkbox">
                                        <input type="checkbox" class="filament-checkbox" id="selectAll">
                                    </th>
                                    <th class="sortable" data-sort="name">
                                        <div class="th-content">
                                            <span>الاسم الكامل</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-sort="national">
                                        <div class="th-content">
                                            <span>الرقم الوطني</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-sort="birth">
                                        <div class="th-content">
                                            <span>تاريخ الميلاد</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th>الجنسية</th>
                                    <th>العنبر</th>
                                    <th>الغرفة</th>
                                    <th>التهمة</th>
                                    <th class="sortable" data-sort="entry">
                                        <div class="th-content">
                                            <span>تاريخ الدخول</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th>الحالة</th>
                                    <th class="actions-header">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="prisonersTableBody">
                                @foreach (var prisoner in Model)
                                {
                                    <tr class="table-row" data-prisoner-id="@prisoner.Id">
                                        <td class="table-checkbox">
                                            <input type="checkbox" class="filament-checkbox row-checkbox" value="@prisoner.Id">
                                        </td>
                                        <td class="prisoner-cell">
                                            <div class="prisoner-info">
                                                <div class="prisoner-avatar">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div class="prisoner-details">
                                                    <div class="prisoner-name">@prisoner.FullName</div>
                                                    <div class="prisoner-number">#@prisoner.PrisonerNumber</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="national-cell">
                                            <span class="national-number">@prisoner.NationalNumber</span>
                                        </td>
                                        <td class="date-cell">
                                            <span class="date-text">@prisoner.DateOfBirth.ToString("yyyy/MM/dd")</span>
                                            <span class="age-text">@prisoner.Age سنة</span>
                                        </td>
                                        <td class="nationality-cell">
                                            <span class="nationality-badge">@prisoner.Nationality</span>
                                        </td>
                                        <td class="ward-cell">
                                            @if (prisoner.Ward != null)
                                            {
                                                <span class="filament-badge filament-badge-primary">
                                                    <i class="fas fa-building"></i>
                                                    @prisoner.Ward.Name
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="filament-badge filament-badge-gray">غير محدد</span>
                                            }
                                        </td>
                                        <td class="room-cell">
                                            @if (prisoner.Room != null)
                                            {
                                                <span class="filament-badge filament-badge-success">
                                                    <i class="fas fa-door-open"></i>
                                                    @prisoner.Room.RoomNumber
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="filament-badge filament-badge-gray">غير محدد</span>
                                            }
                                        </td>
                                        <td class="charge-cell">
                                            @if (prisoner.Cases != null && prisoner.Cases.Any())
                                            {
                                                <div class="charge-info">
                                                    <span class="charge-text">@prisoner.Cases.FirstOrDefault()?.Charge</span>
                                                    @if (prisoner.Cases.Count() > 1)
                                                    {
                                                        <span class="more-cases">+@(prisoner.Cases.Count() - 1) أخرى</span>
                                                    }
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="filament-badge filament-badge-gray">لا توجد قضايا</span>
                                            }
                                        </td>
                                        <td class="date-cell">
                                            <span class="date-text">@prisoner.EntryDate.ToString("yyyy/MM/dd")</span>
                                        </td>
                                        <td class="status-cell">
                                            @{
                                                var status = "محبوس";
                                                var statusClass = "filament-badge-success";
                                                if (prisoner.ExpectedReleaseDate.HasValue && prisoner.ExpectedReleaseDate.Value <= DateTime.Now)
                                                {
                                                    status = "مستحق الإفراج";
                                                    statusClass = "filament-badge-warning";
                                                }
                                            }
                                            <span class="filament-badge @statusClass">
                                                <i class="fas fa-circle status-dot"></i>
                                                @status
                                            </span>
                                        </td>
                                        <td class="actions-cell">
                                            <div class="table-actions-dropdown">
                                                <button class="actions-trigger" onclick="toggleActionsMenu(this)">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="actions-menu">
                                                    <a href="/Prisoners/Details/@prisoner.Id" class="action-item">
                                                        <i class="fas fa-eye"></i>
                                                        عرض التفاصيل
                                                    </a>
                                                    <a href="/Prisoners/Edit/@prisoner.Id" class="action-item">
                                                        <i class="fas fa-edit"></i>
                                                        تعديل
                                                    </a>
                                                    <div class="action-divider"></div>
                                                    <button class="action-item" onclick="movePrisoner(@prisoner.Id)">
                                                        <i class="fas fa-exchange-alt"></i>
                                                        نقل
                                                    </button>
                                                    <button class="action-item danger" onclick="deletePrisoner(@prisoner.Id)">
                                                        <i class="fas fa-trash"></i>
                                                        حذف
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="table-pagination">
                    <div class="pagination-info">
                        <span>عرض <strong>1</strong> إلى <strong>@Model.Count()</strong> من <strong>@Model.Count()</strong> نتيجة</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <span class="pagination-numbers">
                            <button class="pagination-number active">1</button>
                        </span>
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // تحديث التاريخ والوقت
    function updateDateTime() {
        const now = new Date();
        const dateStr = now.toLocaleDateString('ar-SA');
        const timeStr = now.toLocaleTimeString('ar-SA');

        document.getElementById('currentDate').textContent = dateStr;
        document.getElementById('currentTime').textContent = timeStr;
    }

    // تحديث كل ثانية
    setInterval(updateDateTime, 1000);
    updateDateTime();

    // وظائف البحث والفلترة
    function searchPrisoners() {
        const name = document.getElementById('searchName').value;
        const national = document.getElementById('searchNational').value;
        const caseNum = document.getElementById('searchCase').value;
        const ward = document.getElementById('filterWard').value;
        const status = document.getElementById('filterStatus').value;

        // تطبيق الفلترة على الجدول
        const rows = document.querySelectorAll('#prisonersTableBody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            let show = true;

            if (name && !row.querySelector('.prisoner-name').textContent.includes(name)) {
                show = false;
            }

            if (national && !row.cells[2].textContent.includes(national)) {
                show = false;
            }

            if (show) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        document.getElementById('totalResults').textContent = visibleCount;
    }

    function clearSearch() {
        document.getElementById('searchName').value = '';
        document.getElementById('searchNational').value = '';
        document.getElementById('searchCase').value = '';
        document.getElementById('filterWard').value = '';
        document.getElementById('filterStatus').value = '';

        const rows = document.querySelectorAll('#prisonersTableBody tr');
        rows.forEach(row => {
            row.style.display = '';
        });

        document.getElementById('totalResults').textContent = '@Model.Count()';
    }

    // وظائف الإجراءات
    function viewPrisoner(id) {
        window.location.href = '/Prisoners/Details/' + id;
    }

    function editPrisoner(id) {
        window.location.href = '/Prisoners/Edit/' + id;
    }

    function movePrisoner(id) {
        if (confirm('هل تريد نقل هذا النزيل؟')) {
            // تنفيذ عملية النقل
            alert('سيتم تطوير هذه الميزة قريباً');
        }
    }

    function exportToExcel() {
        alert('سيتم تصدير البيانات إلى Excel');
    }

    function printResults() {
        window.print();
    }
</script>



<style>
    /* Laravel Filament Inspired Design */
    :root {
        --filament-primary: #f59e0b;
        --filament-primary-dark: #d97706;
        --filament-secondary: #6b7280;
        --filament-success: #10b981;
        --filament-warning: #f59e0b;
        --filament-danger: #ef4444;
        --filament-info: #3b82f6;
        --filament-gray-50: #f9fafb;
        --filament-gray-100: #f3f4f6;
        --filament-gray-200: #e5e7eb;
        --filament-gray-300: #d1d5db;
        --filament-gray-400: #9ca3af;
        --filament-gray-500: #6b7280;
        --filament-gray-600: #4b5563;
        --filament-gray-700: #374151;
        --filament-gray-800: #1f2937;
        --filament-gray-900: #111827;
        --filament-white: #ffffff;
        --filament-border-radius: 0.5rem;
        --filament-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --filament-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    * {
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
        background-color: var(--filament-gray-50);
        margin: 0;
        padding: 0;
        color: var(--filament-gray-900);
        line-height: 1.6;
    }

    .filament-layout {
        min-height: 100vh;
        background-color: var(--filament-gray-50);
    }

    /* Header Styles */
    .filament-header {
        background: var(--filament-white);
        border-bottom: 1px solid var(--filament-gray-200);
        padding: 1.5rem 0;
        box-shadow: var(--filament-shadow);
    }

    .header-container {
        max-width: 1280px;
        margin: 0 auto;
        padding: 0 1.5rem;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .header-title-section {
        flex: 1;
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: var(--filament-gray-500);
    }

    .breadcrumb-item {
        color: var(--filament-gray-500);
    }

    .breadcrumb-item.active {
        color: var(--filament-gray-900);
        font-weight: 500;
    }

    .breadcrumb-separator {
        font-size: 0.75rem;
        color: var(--filament-gray-400);
    }

    .page-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0 0 0.5rem 0;
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--filament-gray-900);
    }

    .page-icon {
        color: var(--filament-primary);
        font-size: 1.5rem;
    }

    .page-description {
        margin: 0;
        color: var(--filament-gray-600);
        font-size: 1rem;
    }

    .header-actions {
        display: flex;
        gap: 0.75rem;
        align-items: flex-start;
    }

    /* Button Styles */
    .filament-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem 1rem;
        border: 1px solid transparent;
        border-radius: var(--filament-border-radius);
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        white-space: nowrap;
    }

    .filament-btn-primary {
        background-color: var(--filament-primary);
        color: var(--filament-white);
        border-color: var(--filament-primary);
    }

    .filament-btn-primary:hover {
        background-color: var(--filament-primary-dark);
        border-color: var(--filament-primary-dark);
        color: var(--filament-white);
    }

    .filament-btn-secondary {
        background-color: var(--filament-gray-100);
        color: var(--filament-gray-700);
        border-color: var(--filament-gray-300);
    }

    .filament-btn-secondary:hover {
        background-color: var(--filament-gray-200);
        color: var(--filament-gray-800);
    }

    .filament-btn-outline {
        background-color: transparent;
        color: var(--filament-gray-700);
        border-color: var(--filament-gray-300);
    }

    .filament-btn-outline:hover {
        background-color: var(--filament-gray-50);
        color: var(--filament-gray-800);
    }

    /* Main Content */
    .filament-main {
        padding: 2rem 0;
    }

    .content-container {
        max-width: 1280px;
        margin: 0 auto;
        padding: 0 1.5rem;
    }

    /* Card Styles */
    .filament-card {
        background: var(--filament-white);
        border: 1px solid var(--filament-gray-200);
        border-radius: var(--filament-border-radius);
        box-shadow: var(--filament-shadow);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--filament-gray-200);
        background: var(--filament-gray-50);
    }

    .card-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--filament-gray-900);
    }

    .card-content {
        padding: 1.5rem;
    }

    /* Filters */
    .filters-card .card-content {
        padding: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

    .filters-content {
        padding: 1.5rem;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--filament-gray-700);
        margin: 0;
    }

    .input-wrapper {
        position: relative;
    }

    .input-icon {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--filament-gray-400);
        font-size: 0.875rem;
        pointer-events: none;
    }

    .filament-input,
    .filament-select {
        width: 100%;
        padding: 0.625rem 0.75rem;
        padding-right: 2.5rem;
        border: 1px solid var(--filament-gray-300);
        border-radius: var(--filament-border-radius);
        font-size: 0.875rem;
        background-color: var(--filament-white);
        transition: all 0.15s ease-in-out;
    }

    .filament-input:focus,
    .filament-select:focus {
        outline: none;
        border-color: var(--filament-primary);
        box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
    }

    .filters-actions {
        display: flex;
        gap: 0.75rem;
        padding-top: 1rem;
        border-top: 1px solid var(--filament-gray-200);
    }

    .toggle-filters {
        background: none;
        border: none;
        color: var(--filament-gray-500);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: all 0.15s ease-in-out;
    }

    .toggle-filters:hover {
        background-color: var(--filament-gray-100);
        color: var(--filament-gray-700);
    }

    /* Table Styles */
    .table-card .card-header {
        background: var(--filament-white);
        border-bottom: 1px solid var(--filament-gray-200);
    }

    .table-header-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .records-count {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: var(--filament-gray-600);
        background: var(--filament-gray-100);
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
    }

    .table-header-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .table-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .view-toggle {
        display: flex;
        border: 1px solid var(--filament-gray-300);
        border-radius: var(--filament-border-radius);
        overflow: hidden;
    }

    .view-btn {
        padding: 0.5rem;
        border: none;
        background: var(--filament-white);
        color: var(--filament-gray-500);
        cursor: pointer;
        transition: all 0.15s ease-in-out;
    }

    .view-btn.active,
    .view-btn:hover {
        background: var(--filament-primary);
        color: var(--filament-white);
    }

    .table-wrapper {
        overflow: hidden;
    }

    .filament-table-container {
        overflow-x: auto;
    }

    .filament-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;
    }

    .filament-table th {
        background: var(--filament-gray-50);
        color: var(--filament-gray-700);
        font-weight: 600;
        padding: 0.75rem 1rem;
        text-align: right;
        border-bottom: 1px solid var(--filament-gray-200);
        white-space: nowrap;
    }

    .filament-table th.sortable {
        cursor: pointer;
        user-select: none;
    }

    .filament-table th.sortable:hover {
        background: var(--filament-gray-100);
    }

    .th-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
    }

    .sort-icon {
        color: var(--filament-gray-400);
        font-size: 0.75rem;
        transition: color 0.15s ease-in-out;
    }

    .filament-table th.sortable:hover .sort-icon {
        color: var(--filament-gray-600);
    }

    .filament-table td {
        padding: 1rem;
        border-bottom: 1px solid var(--filament-gray-200);
        vertical-align: middle;
    }

    .table-row {
        transition: background-color 0.15s ease-in-out;
    }

    .table-row:hover {
        background: var(--filament-gray-50);
    }

    .table-checkbox {
        width: 3rem;
        text-align: center;
    }

    .filament-checkbox {
        width: 1rem;
        height: 1rem;
        border: 1px solid var(--filament-gray-300);
        border-radius: 0.25rem;
        cursor: pointer;
    }

    .filament-checkbox:checked {
        background-color: var(--filament-primary);
        border-color: var(--filament-primary);
    }

    .actions-header {
        text-align: center;
        width: 5rem;
    }

    /* Table Cell Styles */
    .prisoner-cell {
        min-width: 200px;
    }

    .prisoner-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .prisoner-avatar {
        width: 2.5rem;
        height: 2.5rem;
        background: var(--filament-gray-100);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--filament-gray-500);
        font-size: 1rem;
    }

    .prisoner-details {
        flex: 1;
    }

    .prisoner-name {
        font-weight: 600;
        color: var(--filament-gray-900);
        margin-bottom: 0.25rem;
    }

    .prisoner-number {
        font-size: 0.75rem;
        color: var(--filament-gray-500);
    }

    .national-cell {
        font-family: 'Courier New', monospace;
        font-weight: 500;
    }

    .date-cell {
        text-align: center;
    }

    .date-text {
        display: block;
        font-weight: 500;
        color: var(--filament-gray-900);
    }

    .age-text {
        display: block;
        font-size: 0.75rem;
        color: var(--filament-gray-500);
        margin-top: 0.25rem;
    }

    .nationality-badge {
        background: var(--filament-gray-100);
        color: var(--filament-gray-700);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .charge-info {
        max-width: 200px;
    }

    .charge-text {
        display: block;
        font-weight: 500;
        color: var(--filament-gray-900);
        margin-bottom: 0.25rem;
    }

    .more-cases {
        font-size: 0.75rem;
        color: var(--filament-gray-500);
    }

    /* Filament Badges */
    .filament-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.25rem 0.625rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        white-space: nowrap;
    }

    .filament-badge-primary {
        background: rgba(245, 158, 11, 0.1);
        color: #d97706;
    }

    .filament-badge-success {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
    }

    .filament-badge-warning {
        background: rgba(245, 158, 11, 0.1);
        color: #d97706;
    }

    .filament-badge-danger {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
    }

    .filament-badge-gray {
        background: var(--filament-gray-100);
        color: var(--filament-gray-600);
    }

    .status-dot {
        font-size: 0.5rem;
    }

    /* Actions Dropdown */
    .actions-cell {
        text-align: center;
        position: relative;
    }

    .table-actions-dropdown {
        position: relative;
        display: inline-block;
    }

    .actions-trigger {
        width: 2rem;
        height: 2rem;
        border: none;
        background: transparent;
        color: var(--filament-gray-500);
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .actions-trigger:hover {
        background: var(--filament-gray-100);
        color: var(--filament-gray-700);
    }

    .actions-menu {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: var(--filament-white);
        border: 1px solid var(--filament-gray-200);
        border-radius: var(--filament-border-radius);
        box-shadow: var(--filament-shadow-lg);
        min-width: 10rem;
        z-index: 50;
        opacity: 0;
        visibility: hidden;
        transition: all 0.15s ease-in-out;
        margin-top: 0.25rem;
    }

    .actions-menu.show {
        opacity: 1;
        visibility: visible;
    }

    .action-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        color: var(--filament-gray-700);
        text-decoration: none;
        font-size: 0.875rem;
        border: none;
        background: none;
        width: 100%;
        text-align: right;
        cursor: pointer;
        transition: background-color 0.15s ease-in-out;
    }

    .action-item:hover {
        background: var(--filament-gray-50);
        color: var(--filament-gray-900);
    }

    .action-item.danger {
        color: var(--filament-danger);
    }

    .action-item.danger:hover {
        background: rgba(239, 68, 68, 0.05);
        color: var(--filament-danger);
    }

    .action-divider {
        height: 1px;
        background: var(--filament-gray-200);
        margin: 0.25rem 0;
    }

    /* Pagination */
    .table-pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        border-top: 1px solid var(--filament-gray-200);
        background: var(--filament-gray-50);
    }

    .pagination-info {
        font-size: 0.875rem;
        color: var(--filament-gray-600);
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .pagination-btn {
        width: 2rem;
        height: 2rem;
        border: 1px solid var(--filament-gray-300);
        background: var(--filament-white);
        color: var(--filament-gray-500);
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pagination-btn:not(:disabled):hover {
        background: var(--filament-gray-50);
        color: var(--filament-gray-700);
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-numbers {
        display: flex;
        gap: 0.25rem;
        margin: 0 0.5rem;
    }

    .pagination-number {
        width: 2rem;
        height: 2rem;
        border: 1px solid var(--filament-gray-300);
        background: var(--filament-white);
        color: var(--filament-gray-700);
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-number.active {
        background: var(--filament-primary);
        border-color: var(--filament-primary);
        color: var(--filament-white);
    }

    .pagination-number:not(.active):hover {
        background: var(--filament-gray-50);
    }

    /* Responsive Design */
    @@media (max-width: 1024px) {
        .header-content {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .filters-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .table-header-right {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }
    }

    @@media (max-width: 768px) {
        .content-container {
            padding: 0 1rem;
        }

        .filters-grid {
            grid-template-columns: 1fr;
        }

        .filters-actions {
            flex-direction: column;
        }

        .table-header-left {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .table-actions {
            flex-wrap: wrap;
        }

        .filament-table {
            font-size: 0.75rem;
        }

        .filament-table th,
        .filament-table td {
            padding: 0.5rem;
        }

        .prisoner-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .prisoner-avatar {
            width: 2rem;
            height: 2rem;
        }

        .table-pagination {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
    }

    @@media (max-width: 640px) {
        .filament-main {
            padding: 1rem 0;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .breadcrumb {
            font-size: 0.75rem;
        }

        .card-header {
            padding: 1rem;
        }

        .card-content {
            padding: 1rem;
        }

        .filters-content {
            padding: 1rem;
        }
    }

    /* Print Styles */
    @@media print {
        .filament-header,
        .filters-card,
        .table-actions,
        .actions-cell,
        .table-pagination {
            display: none !important;
        }

        .filament-layout {
            background: white !important;
        }

        .filament-card {
            box-shadow: none !important;
            border: none !important;
        }

        .filament-table {
            font-size: 0.75rem;
        }

        .filament-table th,
        .filament-table td {
            padding: 0.25rem !important;
            border: 1px solid #000 !important;
        }
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .slide-down {
        animation: slideDown 0.3s ease-in-out;
    }

    @@keyframes slideDown {
        from {
            opacity: 0;
            max-height: 0;
        }
        to {
            opacity: 1;
            max-height: 500px;
        }
    }
</style>

<script>
    // Filament-style JavaScript functionality
    document.addEventListener('DOMContentLoaded', function() {
        initializeFilters();
        initializeTable();
        initializeActions();
    });

    function initializeFilters() {
        // Toggle filters visibility
        const toggleBtn = document.querySelector('.toggle-filters');
        const filtersContent = document.getElementById('filtersContent');

        if (toggleBtn && filtersContent) {
            toggleBtn.addEventListener('click', function() {
                const isVisible = filtersContent.style.display !== 'none';
                filtersContent.style.display = isVisible ? 'none' : 'block';

                const icon = toggleBtn.querySelector('i');
                icon.className = isVisible ? 'fas fa-chevron-down' : 'fas fa-chevron-up';
            });
        }

        // Real-time search
        const searchInputs = document.querySelectorAll('.filament-input, .filament-select');
        searchInputs.forEach(input => {
            input.addEventListener('input', debounce(searchPrisoners, 300));
        });
    }

    function initializeTable() {
        // Select all checkbox functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });
        }

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
                updateBulkActions();
            });
        });

        // Sortable columns
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const sortField = this.dataset.sort;
                sortTable(sortField);
            });
        });
    }

    function initializeActions() {
        // Close actions menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.table-actions-dropdown')) {
                document.querySelectorAll('.actions-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
    }

    function toggleActionsMenu(trigger) {
        const menu = trigger.nextElementSibling;
        const isVisible = menu.classList.contains('show');

        // Close all other menus
        document.querySelectorAll('.actions-menu').forEach(m => {
            m.classList.remove('show');
        });

        // Toggle current menu
        if (!isVisible) {
            menu.classList.add('show');
        }
    }

    function searchPrisoners() {
        const searchName = document.getElementById('searchName').value.toLowerCase();
        const searchNational = document.getElementById('searchNational').value.toLowerCase();
        const searchCase = document.getElementById('searchCase').value.toLowerCase();
        const filterWard = document.getElementById('filterWard').value;
        const filterStatus = document.getElementById('filterStatus').value;
        const filterNationality = document.getElementById('filterNationality').value;

        const rows = document.querySelectorAll('#prisonersTableBody .table-row');
        let visibleCount = 0;

        rows.forEach(row => {
            let show = true;

            const name = row.querySelector('.prisoner-name').textContent.toLowerCase();
            const national = row.querySelector('.national-cell').textContent.toLowerCase();
            const nationality = row.querySelector('.nationality-badge').textContent.toLowerCase();

            if (searchName && !name.includes(searchName)) show = false;
            if (searchNational && !national.includes(searchNational)) show = false;
            if (filterNationality && !nationality.includes(filterNationality.toLowerCase())) show = false;

            if (show) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        document.getElementById('totalResults').textContent = visibleCount;
    }

    function clearSearch() {
        document.querySelectorAll('.filament-input, .filament-select').forEach(input => {
            input.value = '';
        });

        document.querySelectorAll('#prisonersTableBody .table-row').forEach(row => {
            row.style.display = '';
        });

        document.getElementById('totalResults').textContent = '@Model.Count()';
    }

    function sortTable(field) {
        // Implement sorting logic here
        console.log('Sorting by:', field);
    }

    function updateBulkActions() {
        const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
        // Update bulk actions UI based on selection
        console.log('Selected items:', checkedCount);
    }

    function exportToExcel() {
        // Implement Excel export
        alert('تصدير إلى Excel - سيتم تطوير هذه الميزة قريباً');
    }

    function printResults() {
        window.print();
    }

    function movePrisoner(id) {
        alert('نقل النزيل - سيتم تطوير هذه الميزة قريباً');
    }

    function deletePrisoner(id) {
        if (confirm('هل أنت متأكد من حذف هذا النزيل؟')) {
            alert('حذف النزيل - سيتم تطوير هذه الميزة قريباً');
        }
    }

    function toggleFilters() {
        const filtersContent = document.getElementById('filtersContent');
        const toggleBtn = document.querySelector('.toggle-filters i');

        if (filtersContent.style.display === 'none') {
            filtersContent.style.display = 'block';
            toggleBtn.className = 'fas fa-chevron-up';
        } else {
            filtersContent.style.display = 'none';
            toggleBtn.className = 'fas fa-chevron-down';
        }
    }

    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
</script>
</style>

@section Scripts {
    <script src="~/js/prisoners-control.js"></script>
}

