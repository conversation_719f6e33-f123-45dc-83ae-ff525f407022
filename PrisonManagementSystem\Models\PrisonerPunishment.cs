using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PrisonManagementSystem.Models
{
    public class PrisonerPunishment
    {
        [Key]
        public int Id { get; set; }

        // العلاقة مع النزيل
        [Required]
        [Display(Name = "النزيل")]
        public int PrisonerId { get; set; }
        [ForeignKey("PrisonerId")]
        public virtual Prisoner Prisoner { get; set; }

        // العلاقة مع الشيلة (غرفة العقوبة)
        [Required]
        [Display(Name = "الشيلة")]
        public int ShilaId { get; set; }
        [ForeignKey("ShilaId")]
        public virtual Shila Shila { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "سبب العقوبة")]
        public string Reason { get; set; }

        [StringLength(500)]
        [Display(Name = "تفاصيل المخالفة")]
        public string ViolationDetails { get; set; }

        [Required]
        [Display(Name = "تاريخ بداية العقوبة")]
        public DateTime StartDate { get; set; }

        [Required]
        [Display(Name = "تاريخ نهاية العقوبة")]
        public DateTime EndDate { get; set; }

        [Display(Name = "مدة العقوبة (بالأيام)")]
        public int DurationDays 
        { 
            get 
            { 
                return (EndDate - StartDate).Days + 1; 
            } 
        }

        [Required]
        [StringLength(100)]
        [Display(Name = "نوع العقوبة")]
        public string PunishmentType { get; set; }

        [Display(Name = "شدة العقوبة")]
        [Range(1, 5)]
        public int Severity { get; set; } = 1; // 1 = خفيفة، 5 = شديدة

        [StringLength(500)]
        [Display(Name = "ملاحظات إضافية")]
        public string Notes { get; set; }

        [Display(Name = "حالة العقوبة")]
        public PunishmentStatus Status { get; set; } = PunishmentStatus.Active;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        [Display(Name = "المسؤول عن العقوبة")]
        public string CreatedBy { get; set; }

        [Display(Name = "تاريخ الانتهاء الفعلي")]
        public DateTime? ActualEndDate { get; set; }

        [StringLength(500)]
        [Display(Name = "سبب الإنهاء المبكر")]
        public string EarlyEndReason { get; set; }

        // خصائص محسوبة
        [Display(Name = "هل العقوبة نشطة")]
        public bool IsActive 
        { 
            get 
            { 
                return Status == PunishmentStatus.Active && DateTime.Now >= StartDate && DateTime.Now <= EndDate; 
            } 
        }

        [Display(Name = "الأيام المتبقية")]
        public int RemainingDays 
        { 
            get 
            { 
                if (Status != PunishmentStatus.Active) return 0;
                var remaining = (EndDate - DateTime.Now).Days;
                return remaining > 0 ? remaining : 0;
            } 
        }

        [Display(Name = "الأيام المنقضية")]
        public int ElapsedDays 
        { 
            get 
            { 
                var elapsed = (DateTime.Now - StartDate).Days;
                return elapsed > 0 ? elapsed : 0;
            } 
        }
    }

    public enum PunishmentStatus
    {
        [Display(Name = "نشطة")]
        Active = 1,
        
        [Display(Name = "مكتملة")]
        Completed = 2,
        
        [Display(Name = "ملغاة")]
        Cancelled = 3,
        
        [Display(Name = "مؤجلة")]
        Suspended = 4,
        
        [Display(Name = "منتهية مبكراً")]
        EarlyTerminated = 5
    }
}
