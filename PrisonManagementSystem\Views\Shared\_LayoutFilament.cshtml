<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة سجن الكويفية</title>
    <link rel="icon" type="image/svg+xml" href="~/images/logo-icon.svg">
    <link rel="alternate icon" href="~/favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/css/arabic-typography.css" />
    <link rel="stylesheet" href="~/css/filament.css" />
    <link rel="stylesheet" href="~/css/filament-enhanced.css" />
    <link rel="stylesheet" href="~/css/professional-effects.css" />
    <link rel="stylesheet" href="~/css/enhanced-forms.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/filament-theme.css" />
    <link rel="stylesheet" href="~/css/filament-forms.css" />
    <link rel="stylesheet" href="~/css/dashboard.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <style>
        /* Enhanced Typography */
        body {
            font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-optical-sizing: auto;
            font-variation-settings: "slnt" 0;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Enhanced Sidebar */
        .filament-sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .filament-header-brand {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            background: rgba(255, 255, 255, 0.05);
        }

        .filament-nav-item {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 600 !important;
            font-size: 0.95rem !important;
            color: #ffffff !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            border-radius: 0.75rem !important;
            margin: 0.25rem 0.75rem !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        .filament-nav-item:hover {
            background: rgba(99, 102, 241, 0.15) !important;
            color: #ffffff !important;
            transform: translateX(-4px) !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
        }

        .filament-nav-item.active {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%) !important;
            color: #ffffff !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
            font-weight: 700 !important;
        }

        .filament-nav-group-label {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 800 !important;
            font-size: 0.8rem !important;
            text-transform: uppercase !important;
            letter-spacing: 0.1em !important;
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6) !important;
            margin-bottom: 0.5rem !important;
        }

        /* Enhanced Navigation Icons */
        .filament-nav-item i {
            color: #ffffff !important;
            font-size: 1rem !important;
            margin-left: 0.75rem !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
            transition: all 0.3s ease !important;
        }

        .filament-nav-item:hover i {
            color: #ffffff !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
            transform: scale(1.1) !important;
        }

        .filament-nav-item.active i {
            color: #ffffff !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
        }

        /* Enhanced Sidebar Background */
        .filament-sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        /* Enhanced Text Contrast */
        .filament-nav-item,
        .filament-nav-item span,
        .filament-nav-item a {
            color: #ffffff !important;
            text-decoration: none !important;
            font-weight: 600 !important;
        }

        /* Enhanced Button Text */
        .filament-nav-item button {
            color: #ffffff !important;
            font-family: 'Cairo', sans-serif !important;
            font-weight: 600 !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        .filament-nav-item button:hover {
            color: #ffffff !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
        }

        /* Enhanced Navigation Group */
        .filament-nav-group {
            margin-bottom: 1.5rem !important;
        }

        /* Enhanced Brand Text */
        .filament-brand-text {
            color: #ffffff !important;
            font-weight: 700 !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
        }

        /* Additional Text Elements - Force White Text */
        .filament-sidebar *,
        .filament-sidebar a,
        .filament-sidebar span,
        .filament-sidebar div,
        .filament-sidebar button,
        .filament-sidebar .text-white,
        .filament-sidebar .navbar-brand,
        .filament-sidebar .nav-link {
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        /* Override any inherited colors */
        .filament-sidebar .filament-nav-item,
        .filament-sidebar .filament-nav-item *,
        .filament-sidebar .filament-nav-group-label,
        .filament-sidebar .filament-brand-text,
        .filament-sidebar h1,
        .filament-sidebar h2,
        .filament-sidebar h3,
        .filament-sidebar h4,
        .filament-sidebar h5,
        .filament-sidebar h6,
        .filament-sidebar p {
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        /* Enhanced Main Content */
        .filament-main-content {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
        }

        /* Enhanced Animations */
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @@keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @@keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .filament-page > * {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .filament-header {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .filament-stat-card-enhanced:nth-child(1) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both; }
        .filament-stat-card-enhanced:nth-child(2) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both; }
        .filament-stat-card-enhanced:nth-child(3) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both; }
        .filament-stat-card-enhanced:nth-child(4) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both; }

        .filament-table-row {
            animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Enhanced Page Transitions */
        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Loading States */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #6366f1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="filament-main">
    <!-- Sidebar -->
    <aside class="filament-sidebar">
        <div class="filament-header-brand" style="padding: 24px;">
            <img src="~/images/logo.svg" alt="شعار القيادة العامة للقوات المسلحة العربية الليبية" class="app-logo">
            <div>
                <h3 class="filament-header-title" style="font-size: 16px; margin: 0;">نظام إدارة سجن الكويفية</h3>
                <p style="font-size: 12px; color: var(--gray-500); margin: 0;">الكتيبة 210 مشاة آلية</p>
            </div>
        </div>
        
        <nav class="filament-nav">
            <div class="filament-nav-group">
                <div class="filament-nav-group-label">القائمة الرئيسية</div>
                <a href="@Url.Action("Index", "Home")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
                <a href="@Url.Action("Dashboard", "Home")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "Dashboard" ? "active" : "")">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="@Url.Action("Control", "Prisoners")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Prisoners" && ViewContext.RouteData.Values["Action"]?.ToString() == "Control" ? "active" : "")">
                    <i class="fas fa-users"></i>
                    كنترول النزلاء
                </a>
            </div>
            
            <div class="filament-nav-group">
                <div class="filament-nav-group-label">إدارة النزلاء</div>
                <a href="@Url.Action("Index", "Prisoners")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Prisoners" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")">
                    <i class="fas fa-user"></i>
                    النزلاء
                </a>
                <a href="@Url.Action("Index", "PrisonerCases")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "PrisonerCases" ? "active" : "")">
                    <i class="fas fa-gavel"></i>
                    القضايا
                </a>
                <a href="@Url.Action("Index", "PrisonerMovements")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "PrisonerMovements" ? "active" : "")">
                    <i class="fas fa-exchange-alt"></i>
                    الحركات
                </a>
            </div>
            
            <div class="filament-nav-group">
                <div class="filament-nav-group-label">إدارة المرافق</div>
                <a href="@Url.Action("Index", "Wards")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Wards" ? "active" : "")">
                    <i class="fas fa-building"></i>
                    العنابر
                </a>
                <a href="@Url.Action("Index", "Rooms")" class="filament-nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Rooms" ? "active" : "")">
                    <i class="fas fa-door-open"></i>
                    الغرف
                </a>
            </div>
            
            <div class="filament-nav-group">
                <div class="filament-nav-group-label">النظام</div>
                <a href="@Url.Action("Privacy", "Home")" class="filament-nav-item">
                    <i class="fas fa-shield-alt"></i>
                    الخصوصية
                </a>
                @if (User.Identity.IsAuthenticated)
                {
                    <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home")" method="post" style="display: inline;">
                        <button type="submit" class="filament-nav-item" style="background: none; border: none; width: 100%; text-align: right;">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </button>
                    </form>
                }
                else
                {
                    <a href="@Url.Action("Login", "Account")" class="filament-nav-item">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </a>
                }
            </div>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="filament-content">
        <!-- Header -->
        <header class="filament-header">
            <div class="filament-header-brand">
                <h1 class="filament-header-title">@ViewData["Title"]</h1>
            </div>
            <div class="flex items-center gap-4">
                @if (User.Identity.IsAuthenticated)
                {
                    <div class="flex items-center gap-2">
                        <i class="fas fa-user-circle" style="font-size: 24px; color: var(--gray-600);"></i>
                        <span style="color: var(--gray-700); font-weight: 500;">@User.Identity.Name</span>
                    </div>
                }
                <button class="filament-btn filament-btn-secondary d-lg-none" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </header>

        <!-- Page Content -->
        <div class="filament-fade-in">
            @RenderBody()
        </div>
    </main>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.filament-sidebar');
            sidebar.classList.toggle('open');
        }

        // Enhanced UI Interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Page Load Animation
            document.body.classList.add('page-transition');
            setTimeout(() => document.body.classList.add('loaded'), 100);

            // Enhanced form interactions
            const formInputs = document.querySelectorAll('input, textarea, select');
            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.closest('.filament-form-field')?.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.closest('.filament-form-field')?.classList.remove('focused');
                });
            });

            // Enhanced button ripple effect
            const buttons = document.querySelectorAll('.filament-button, .btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.3);
                        transform: scale(0);
                        animation: ripple-animation 0.6s linear;
                        pointer-events: none;
                    `;

                    this.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 600);
                });
            });

            // Enhanced table interactions
            const tableRows = document.querySelectorAll('.filament-table-row');
            tableRows.forEach((row, index) => {
                row.style.animationDelay = `${index * 0.05}s`;
            });

            // Enhanced search functionality
            const searchInputs = document.querySelectorAll('.filament-search-field');
            searchInputs.forEach(input => {
                let searchTimeout;
                input.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = this.value.toLowerCase();

                    searchTimeout = setTimeout(() => {
                        const searchableElements = document.querySelectorAll('.filament-table-row, .filament-ward-card, .filament-room-card');
                        searchableElements.forEach(element => {
                            const isVisible = element.textContent.toLowerCase().includes(searchTerm);
                            element.style.display = isVisible ? '' : 'none';
                        });
                    }, 300);
                });
            });
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.filament-sidebar');
            const toggleBtn = event.target.closest('[onclick="toggleSidebar()"]');

            if (!sidebar.contains(event.target) && !toggleBtn && window.innerWidth <= 1024) {
                sidebar.classList.remove('open');
            }
        });
    </script>

    <style>
        @@keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
