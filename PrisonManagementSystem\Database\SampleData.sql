-- سكريبت إدراج بيانات تجريبية
-- Sample Data Insertion Script

USE PrisonManagementDB;
GO

-- إدراج نزلاء تجريبيين
INSERT INTO Prisoners (
    Prisoner<PERSON><PERSON>ber, FullName, MotherName, DateOfBirth, PlaceOfBirth, Nationality,
    NationalNumber, HealthStatus, EntryDate, HasPreviousImprisonment,
    WardId, RoomId, CreatedBy
) VALUES
('20240001', 'أحمد محمد علي حسن', 'فاطمة أحمد', '1985-03-15', 'بغداد', 'عراقي',
 '198503151234567', 'سليم', '2024-01-15 10:30:00', 0, 1, 1, 'admin'),

('20240002', 'محمد عبدالله سالم', 'زينب محمد', '1990-07-22', 'البصرة', 'عراقي',
 '199007221234568', 'يعاني من ضغط الدم', '2024-02-10 14:20:00', 1, 1, 2, 'admin'),

('20240003', 'علي حسين جابر', 'مريم علي', '1988-11-08', 'الموصل', 'عراقي',
 '198811081234569', 'سليم', '2024-03-05 09:15:00', 0, 2, 3, 'admin'),

('20240004', 'حسن عبدالرحمن', 'خديجة حسن', '1992-05-30', 'دمشق', 'سوري',
 '199205301234570', 'يعاني من السكري', '2024-03-20 16:45:00', 0, 2, 4, 'admin'),

('20240005', 'عمر صالح محمود', 'عائشة عمر', '1987-09-12', 'حلب', 'سوري',
 '198709121234571', 'سليم', '2024-04-01 11:30:00', 1, 3, 5, 'admin');

-- إدراج قضايا للنزلاء
INSERT INTO PrisonerCases (
    CaseNumber, Charge, ChargeDetails, Verdict, SentenceDays, VerdictStatus,
    CaseDate, Court, Judge, PrisonerId, CreatedBy
) VALUES
-- قضايا السجين الأول
('2024/001/جنايات', 'سرقة', 'سرقة مبلغ مالي من محل تجاري', 'سنتان سجن', 730, 'محكوم',
 '2024-01-10', 'محكمة جنايات بغداد', 'القاضي أحمد السعدي', 1, 'admin'),

-- قضايا السجين الثاني
('2024/002/جنايات', 'اعتداء', 'اعتداء بالضرب على شخص', 'سنة ونصف سجن', 548, 'محكوم',
 '2024-02-05', 'محكمة جنايات البصرة', 'القاضي محمد الكاظمي', 2, 'admin'),

('2024/003/جنايات', 'إتلاف ممتلكات', 'إتلاف سيارة', '6 أشهر سجن', 180, 'محكوم',
 '2024-02-08', 'محكمة جنايات البصرة', 'القاضي محمد الكاظمي', 2, 'admin'),

-- قضايا السجين الثالث
('2024/004/جنح', 'مخالفة مرورية', 'قيادة بدون رخصة', '3 أشهر سجن', 90, 'محكوم',
 '2024-03-01', 'محكمة جنح الموصل', 'القاضي علي الجبوري', 3, 'admin'),

-- قضايا السجين الرابع
('2024/005/جنايات', 'تهريب', 'تهريب بضائع عبر الحدود', 'قيد المحاكمة', NULL, 'موقوف',
 '2024-03-15', 'محكمة جنايات دمشق', 'القاضي خالد الأسد', 4, 'admin'),

-- قضايا السجين الخامس
('2024/006/جنايات', 'احتيال', 'احتيال مالي على مواطنين', 'سنتان ونصف سجن', 913, 'محكوم',
 '2024-03-25', 'محكمة جنايات حلب', 'القاضي عمر الشامي', 5, 'admin');

-- إدراج حركات تجريبية
INSERT INTO PrisonerMovements (
    MovementType, MovementCategory, MovementDate, Destination, Reason, Status,
    PrisonerId, CreatedBy
) VALUES
('نقل داخلي', 'نقل داخلي', '2024-02-01 10:00:00', 'العنبر الثاني', 'نقل لأسباب أمنية', 'مكتملة', 1, 'admin'),
('علاج خارجي', 'علاج خارجي', '2024-03-15 08:30:00', 'مستشفى بغداد التعليمي', 'فحص طبي دوري', 'مكتملة', 2, 'admin'),
('مأمورية', 'مأمورية', '2024-04-10 09:00:00', 'محكمة الجنايات', 'حضور جلسة محاكمة', 'مكتملة', 4, 'admin');

-- تحديث تواريخ الإفراج المتوقعة
UPDATE Prisoners 
SET ExpectedReleaseDate = DATEADD(day, 
    (SELECT SUM(SentenceDays) FROM PrisonerCases WHERE PrisonerId = Prisoners.Id AND VerdictStatus = 'محكوم'), 
    EntryDate),
    TotalSentenceDays = (SELECT SUM(SentenceDays) FROM PrisonerCases WHERE PrisonerId = Prisoners.Id AND VerdictStatus = 'محكوم')
WHERE Id IN (1, 2, 3, 5);

-- تحديث عدد النزلاء في العنابر والغرف
UPDATE Wards SET CurrentCount = (
    SELECT COUNT(*) FROM Prisoners WHERE WardId = Wards.Id
);

UPDATE Rooms SET CurrentCount = (
    SELECT COUNT(*) FROM Prisoners WHERE RoomId = Rooms.Id
);

-- إدراج شيل تجريبية
INSERT INTO Shilas (Name, Description, MaxCapacity, CurrentCount, RoomId) VALUES
('شيلة أ', 'شيلة أ - غرفة 101', 5, 0, 1),
('شيلة ب', 'شيلة ب - غرفة 101', 5, 0, 1),
('شيلة أ', 'شيلة أ - غرفة 102', 5, 0, 2),
('شيلة ب', 'شيلة ب - غرفة 102', 5, 0, 2),
('شيلة أ', 'شيلة أ - غرفة 201', 4, 0, 3),
('شيلة ب', 'شيلة ب - غرفة 201', 4, 0, 3);

GO

-- إنشاء view لعرض معلومات النزلاء الشاملة
CREATE VIEW vw_PrisonersDetails AS
SELECT 
    p.Id,
    p.PrisonerNumber,
    p.FullName,
    p.MotherName,
    p.DateOfBirth,
    DATEDIFF(YEAR, p.DateOfBirth, GETDATE()) AS Age,
    p.Nationality,
    p.NationalNumber,
    p.EntryDate,
    p.ExpectedReleaseDate,
    p.TotalSentenceDays,
    w.Name AS WardName,
    r.RoomNumber,
    p.HealthStatus,
    CASE 
        WHEN p.ExpectedReleaseDate IS NOT NULL AND p.ExpectedReleaseDate <= GETDATE() THEN 'مستحق الإفراج'
        WHEN p.ExpectedReleaseDate IS NULL THEN 'موقوف'
        ELSE 'محبوس'
    END AS Status,
    (SELECT COUNT(*) FROM PrisonerCases WHERE PrisonerId = p.Id AND IsActive = 1) AS CasesCount,
    (SELECT TOP 1 Charge FROM PrisonerCases WHERE PrisonerId = p.Id AND IsActive = 1 ORDER BY CaseDate DESC) AS LatestCharge
FROM Prisoners p
LEFT JOIN Wards w ON p.WardId = w.Id
LEFT JOIN Rooms r ON p.RoomId = r.Id;

GO

-- إنشاء view لإحصائيات العنابر
CREATE VIEW vw_WardsStatistics AS
SELECT 
    w.Id,
    w.Name,
    w.MaxCapacity,
    w.CurrentCount,
    CASE 
        WHEN w.MaxCapacity > 0 THEN CAST(w.CurrentCount * 100.0 / w.MaxCapacity AS DECIMAL(5,2))
        ELSE 0
    END AS OccupancyRate,
    (SELECT COUNT(*) FROM Rooms WHERE WardId = w.Id AND IsActive = 1) AS RoomsCount,
    w.IsActive
FROM Wards w;

PRINT 'تم إدراج البيانات التجريبية بنجاح!';
PRINT 'Sample Data Inserted Successfully!';
PRINT '';
PRINT 'النزلاء المدرجين: 5';
PRINT 'القضايا المدرجة: 6';
PRINT 'الحركات المدرجة: 3';
PRINT 'الشيل المدرجة: 6';
GO
