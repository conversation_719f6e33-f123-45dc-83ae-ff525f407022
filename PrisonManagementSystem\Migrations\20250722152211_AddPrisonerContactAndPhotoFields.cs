﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PrisonManagementSystem.Migrations
{
    public partial class AddPrisonerContactAndPhotoFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ClosestRelativeAddress",
                table: "Prisoners",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClosestRelativeName",
                table: "Prisoners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClosestRelativePhone",
                table: "Prisoners",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                table: "Prisoners",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhotoPath",
                table: "Prisoners",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RelationshipType",
                table: "Prisoners",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CaseDescription",
                table: "PrisonerCases",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CaseType",
                table: "PrisonerCases",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CourtName",
                table: "PrisonerCases",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "JudgeName",
                table: "PrisonerCases",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SentenceDuration",
                table: "PrisonerCases",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SentenceType",
                table: "PrisonerCases",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ClosestRelativeAddress",
                table: "Prisoners");

            migrationBuilder.DropColumn(
                name: "ClosestRelativeName",
                table: "Prisoners");

            migrationBuilder.DropColumn(
                name: "ClosestRelativePhone",
                table: "Prisoners");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "Prisoners");

            migrationBuilder.DropColumn(
                name: "PhotoPath",
                table: "Prisoners");

            migrationBuilder.DropColumn(
                name: "RelationshipType",
                table: "Prisoners");

            migrationBuilder.DropColumn(
                name: "CaseDescription",
                table: "PrisonerCases");

            migrationBuilder.DropColumn(
                name: "CaseType",
                table: "PrisonerCases");

            migrationBuilder.DropColumn(
                name: "CourtName",
                table: "PrisonerCases");

            migrationBuilder.DropColumn(
                name: "JudgeName",
                table: "PrisonerCases");

            migrationBuilder.DropColumn(
                name: "SentenceDuration",
                table: "PrisonerCases");

            migrationBuilder.DropColumn(
                name: "SentenceType",
                table: "PrisonerCases");
        }
    }
}
