@model IEnumerable<PrisonManagementSystem.Models.Prisoner>

@{
    ViewData["Title"] = "قائمة النزلاء";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="filament-page filament-typography-enhanced">
    <!-- Header Section -->
    <div class="filament-header filament-header-enhanced">
        <div class="filament-header-content">
            <div class="filament-header-heading">
                <h1 class="filament-title-enhanced">
                    <svg class="filament-header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    قائمة النزلاء
                </h1>
                <p class="filament-subtitle-enhanced">إدارة ومتابعة شاملة لجميع النزلاء في النظام مع أحدث التقنيات</p>
            </div>
            <div class="filament-header-actions">
                <a href="@Url.Action("Create")" class="filament-button filament-button-primary filament-button-enhanced">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة نزيل جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="filament-stats-enhanced">
        <div class="filament-stat-card-enhanced blue">
            <div class="filament-stat-content-enhanced">
                <div class="filament-stat-icon-enhanced">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stat-value-enhanced">@Model.Count()</div>
                    <div class="filament-stat-label-enhanced">إجمالي النزلاء</div>
                </div>
            </div>
        </div>

        <div class="filament-stat-card-enhanced green">
            <div class="filament-stat-content-enhanced">
                <div class="filament-stat-icon-enhanced">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stat-value-enhanced">@Model.Count(p => p.Status == "محبوس")</div>
                    <div class="filament-stat-label-enhanced">محبوسين حالياً</div>
                </div>
            </div>
        </div>

        <div class="filament-stat-card-enhanced yellow">
            <div class="filament-stat-content-enhanced">
                <div class="filament-stat-icon-enhanced">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stat-value-enhanced">@Model.Count(p => p.Status == "مستحق الإفراج")</div>
                    <div class="filament-stat-label-enhanced">مستحقين الإفراج</div>
                </div>
            </div>
        </div>

        <div class="filament-stat-card-enhanced red">
            <div class="filament-stat-content-enhanced">
                <div class="filament-stat-icon-enhanced">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stat-value-enhanced">@Model.Count(p => p.HasPreviousImprisonment)</div>
                    <div class="filament-stat-label-enhanced">سوابق سجن</div>
                </div>
            </div>
        </div>
    </div>
    <!-- Main Content -->
    <div class="filament-card">
        <div class="filament-card-header">
            <div class="filament-card-header-content">
                <h3 class="filament-card-title">جدول النزلاء</h3>
                <p class="filament-card-description">قائمة شاملة بجميع النزلاء المسجلين في النظام</p>
            </div>
            <div class="filament-card-header-actions">
                <div class="filament-search-input">
                    <svg class="filament-search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" placeholder="البحث في النزلاء..." class="filament-search-field" id="searchInput">
                </div>
            </div>
        </div>

        @if (Model.Any())
        {
            <div class="filament-table-container">
                <table class="filament-table filament-table-enhanced">
                    <thead class="filament-table-header">
                        <tr>
                            <th class="filament-table-header-cell">رقم السجين</th>
                            <th class="filament-table-header-cell">الاسم الكامل</th>
                            <th class="filament-table-header-cell">العمر</th>
                            <th class="filament-table-header-cell">الجنسية</th>
                            <th class="filament-table-header-cell">العنبر</th>
                            <th class="filament-table-header-cell">الغرفة</th>
                            <th class="filament-table-header-cell">تاريخ الدخول</th>
                            <th class="filament-table-header-cell">الحالة</th>
                            <th class="filament-table-header-cell">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="filament-table-body">
                        @foreach (var item in Model)
                        {
                            <tr class="filament-table-row">
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">
                                        <span class="filament-badge filament-badge-primary">@item.PrisonerNumber</span>
                                    </div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">
                                        <div class="filament-table-cell-primary">@item.FullName</div>
                                        <div class="filament-table-cell-secondary">@item.MotherName</div>
                                    </div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@item.Age سنة</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@item.Nationality</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@(item.Ward?.Name ?? "غير محدد")</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@(item.Room?.RoomNumber ?? "غير محدد")</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@item.EntryDate.ToString("yyyy/MM/dd")</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">
                                        @if (item.Status == "مستحق الإفراج")
                                        {
                                            <span class="filament-badge filament-badge-warning">مستحق الإفراج</span>
                                        }
                                        else
                                        {
                                            <span class="filament-badge filament-badge-success">محبوس</span>
                                        }
                                    </div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-actions">
                                        <a href="@Url.Action("Details", new { id = item.Id })" class="filament-table-action filament-table-action-view" title="عرض التفاصيل">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </a>
                                        <a href="@Url.Action("Edit", new { id = item.Id })" class="filament-table-action filament-table-action-edit" title="تعديل">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                        <a href="@Url.Action("Delete", new { id = item.Id })" class="filament-table-action filament-table-action-delete" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا النزيل؟')">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="filament-empty-state">
                <div class="filament-empty-state-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="filament-empty-state-title">لا توجد نزلاء مسجلين</h3>
                <p class="filament-empty-state-description">ابدأ بإضافة أول نزيل في النظام</p>
                <a href="@Url.Action("Create")" class="filament-button filament-button-primary">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة نزيل جديد
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        // البحث في الجدول
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('.filament-table-row');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // تأثيرات تفاعلية
        document.querySelectorAll('.filament-table-row').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
}
