/* Enhanced Filament Styles - Professional Typography & Layout */

/* Import Cairo Font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* Global Font Application - Apply Cairo to Everything */
* {
    font-family: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif !important;
}

body,
h1, h2, h3, h4, h5, h6,
p, span, div, a, button, input, textarea, select, label,
.btn, .form-control, .form-select, .form-label,
.nav-link, .navbar-brand, .dropdown-item,
.card-title, .card-text, .card-header,
.table, th, td, .badge, .alert,
.filament-sidebar-nav, .filament-sidebar-nav-item,
.filament-header-title, .filament-stats-card,
.filament-table, .filament-form-label, .filament-form-input,
.filament-section-title, .filament-section-description {
    font-family: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif !important;
}

/* Advanced Typography */
.filament-typography-enhanced {
    font-family: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 1, "tnum" 0, "onum" 1, "lnum" 0, "dlig" 0;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Headers */
.filament-header-enhanced {
    background: white;
    border-radius: 1.5rem;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 10px 25px rgba(0, 0, 0, 0.05),
        0 20px 40px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(99, 102, 241, 0.1);
    position: relative;
    overflow: hidden;
}

.filament-header-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, 
        #6366f1 0%, 
        #8b5cf6 25%, 
        #06b6d4 50%, 
        #10b981 75%, 
        #f59e0b 100%);
}

.filament-header-enhanced::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.03) 0%, transparent 70%);
    pointer-events: none;
}

/* Enhanced Title Styling */
.filament-title-enhanced {
    font-family: 'Cairo', sans-serif;
    font-weight: 800;
    font-size: clamp(1.75rem, 4vw, 3rem);
    background: linear-gradient(135deg, #1f2937 0%, #4f46e5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    letter-spacing: -0.02em;
    margin-bottom: 0.75rem;
}

.filament-subtitle-enhanced {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 1.125rem;
    color: #64748b;
    line-height: 1.6;
}

/* Enhanced Stats Cards */
.filament-stats-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.filament-stat-card-enhanced {
    background: white;
    border-radius: 1.25rem;
    padding: 2.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 4px 12px rgba(0, 0, 0, 0.03);
}

.filament-stat-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-gradient, linear-gradient(90deg, #6366f1, #8b5cf6));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.filament-stat-card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.08);
}

.filament-stat-card-enhanced:hover::before {
    opacity: 1;
}

.filament-stat-card-enhanced.blue {
    --card-gradient: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.filament-stat-card-enhanced.green {
    --card-gradient: linear-gradient(135deg, #10b981, #047857);
}

.filament-stat-card-enhanced.yellow {
    --card-gradient: linear-gradient(135deg, #f59e0b, #d97706);
}

.filament-stat-card-enhanced.red {
    --card-gradient: linear-gradient(135deg, #ef4444, #dc2626);
}

.filament-stat-card-enhanced.purple {
    --card-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Enhanced Stat Content */
.filament-stat-content-enhanced {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.filament-stat-icon-enhanced {
    width: 5rem;
    height: 5rem;
    border-radius: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.filament-stat-icon-enhanced svg {
    width: 2.5rem;
    height: 2.5rem;
    z-index: 2;
    position: relative;
}

.filament-stat-icon-enhanced::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--card-gradient);
    opacity: 0.1;
    border-radius: inherit;
}

.filament-stat-value-enhanced {
    font-family: 'Cairo', sans-serif;
    font-weight: 900;
    font-size: clamp(2rem, 5vw, 3.5rem);
    line-height: 1;
    background: var(--card-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.filament-stat-label-enhanced {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced Tables */
.filament-table-enhanced {
    background: white;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 4px 12px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.filament-table-enhanced thead {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.filament-table-enhanced th {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #374151;
    padding: 1.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.filament-table-enhanced td {
    padding: 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

.filament-table-enhanced tbody tr {
    transition: all 0.2s ease;
}

.filament-table-enhanced tbody tr:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
    transform: scale(1.001);
}

/* Enhanced Forms */
.filament-form-enhanced .filament-section {
    background: white;
    border-radius: 1.25rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 4px 12px rgba(0, 0, 0, 0.03);
    overflow: hidden;
    transition: all 0.3s ease;
}

.filament-form-enhanced .filament-section:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 8px 24px rgba(0, 0, 0, 0.05);
}

.filament-form-enhanced .filament-section-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;
    position: relative;
}

.filament-form-enhanced .filament-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
}

.filament-form-enhanced .filament-form-input,
.filament-form-enhanced .filament-form-select,
.filament-form-enhanced .filament-form-textarea {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filament-form-enhanced .filament-form-input:focus,
.filament-form-enhanced .filament-form-select:focus,
.filament-form-enhanced .filament-form-textarea:focus {
    border-color: #6366f1;
    box-shadow: 
        0 0 0 3px rgba(99, 102, 241, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

/* Enhanced Buttons */
.filament-button-enhanced {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filament-button-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.filament-button-enhanced:hover::before {
    left: 100%;
}

.filament-button-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .filament-header-enhanced {
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .filament-stats-enhanced {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .filament-stat-card-enhanced {
        padding: 2rem;
    }
    
    .filament-stat-content-enhanced {
        gap: 1.5rem;
    }
    
    .filament-stat-icon-enhanced {
        width: 4rem;
        height: 4rem;
    }
    
    .filament-stat-icon-enhanced svg {
        width: 2rem;
        height: 2rem;
    }
}

/* Print Styles */
@media print {
    .filament-header-enhanced,
    .filament-stat-card-enhanced,
    .filament-table-enhanced {
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
    }
    
    .filament-button-enhanced {
        display: none !important;
    }
}
