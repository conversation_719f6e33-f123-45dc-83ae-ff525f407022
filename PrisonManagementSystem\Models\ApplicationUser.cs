using Microsoft.AspNetCore.Identity;
using System;
using System.ComponentModel.DataAnnotations;

namespace PrisonManagementSystem.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; }

        [StringLength(50)]
        [Display(Name = "الرقم الوظيفي")]
        public string EmployeeNumber { get; set; }

        [StringLength(100)]
        [Display(Name = "المنصب")]
        public string Position { get; set; }

        [StringLength(100)]
        [Display(Name = "القسم")]
        public string Department { get; set; }

        [Display(Name = "تاريخ التعيين")]
        public DateTime? HireDate { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ آخر دخول")]
        public DateTime? LastLoginDate { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string Notes { get; set; }
    }
}
