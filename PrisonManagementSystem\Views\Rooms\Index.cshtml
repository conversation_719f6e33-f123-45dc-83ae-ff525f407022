@model IEnumerable<PrisonManagementSystem.Models.Room>

@{
    ViewData["Title"] = "إدارة الغرف";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="filament-page">
    <!-- Header Section -->
    <div class="filament-header">
        <div class="filament-header-content">
            <div class="filament-header-heading">
                <h1 class="filament-header-title">
                    <svg class="filament-header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    إدارة الغرف
                </h1>
                <p class="filament-header-subtitle">إدارة ومتابعة جميع الغرف في العنابر</p>
            </div>
            <div class="filament-header-actions">
                <a href="@Url.Action("Create")" class="filament-button filament-button-primary">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة غرفة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="filament-stats-grid">
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-blue">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Count()</div>
                    <div class="filament-stats-label">إجمالي الغرف</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-green">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Count(r => r.IsActive)</div>
                    <div class="filament-stats-label">غرف نشطة</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-yellow">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Sum(r => r.CurrentCount)</div>
                    <div class="filament-stats-label">إجمالي النزلاء</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-purple">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Sum(r => r.MaxCapacity)</div>
                    <div class="filament-stats-label">السعة الإجمالية</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="filament-card">
        <div class="filament-card-header">
            <div class="filament-card-header-content">
                <h3 class="filament-card-title">قائمة الغرف</h3>
                <p class="filament-card-description">إدارة وتنظيم الغرف في العنابر</p>
            </div>
            <div class="filament-card-header-actions">
                <div class="filament-search-input">
                    <svg class="filament-search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" placeholder="البحث في الغرف..." class="filament-search-field" id="searchInput">
                </div>
            </div>
        </div>

        @if (Model.Any())
        {
            <!-- Grid View -->
            <div class="filament-grid-container">
                @foreach (var room in Model)
                {
                    <div class="filament-room-card">
                        <div class="filament-room-card-header">
                            <div class="filament-room-card-title">
                                <h4>@room.RoomNumber</h4>
                                @if (room.IsActive)
                                {
                                    <span class="filament-badge filament-badge-success">نشطة</span>
                                }
                                else
                                {
                                    <span class="filament-badge filament-badge-danger">غير نشطة</span>
                                }
                            </div>
                            <div class="filament-room-card-actions">
                                <a href="@Url.Action("Details", new { id = room.Id })" class="filament-room-action" title="عرض التفاصيل">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                                <a href="@Url.Action("Edit", new { id = room.Id })" class="filament-room-action" title="تعديل">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        
                        <div class="filament-room-card-content">
                            <div class="filament-room-ward">
                                <span class="filament-room-ward-label">العنبر:</span>
                                <span class="filament-room-ward-name">@(room.Ward?.Name ?? "غير محدد")</span>
                            </div>
                            
                            <p class="filament-room-description">@(room.Description ?? "لا يوجد وصف")</p>
                            
                            <div class="filament-room-stats">
                                <div class="filament-room-stat">
                                    <span class="filament-room-stat-label">العدد الحالي</span>
                                    <span class="filament-room-stat-value">@room.CurrentCount</span>
                                </div>
                                <div class="filament-room-stat">
                                    <span class="filament-room-stat-label">السعة القصوى</span>
                                    <span class="filament-room-stat-value">@room.MaxCapacity</span>
                                </div>
                            </div>
                            
                            <div class="filament-room-progress">
                                <div class="filament-room-progress-bar">
                                    @{
                                        var occupancyPercentage = room.MaxCapacity > 0 ? (room.CurrentCount * 100.0 / room.MaxCapacity) : 0;
                                        var progressClass = occupancyPercentage > 80 ? "filament-progress-danger" : 
                                                          occupancyPercentage > 60 ? "filament-progress-warning" : "filament-progress-success";
                                    }
                                    <div class="filament-room-progress-fill @progressClass" style="width: @occupancyPercentage%"></div>
                                </div>
                                <span class="filament-room-progress-text">@occupancyPercentage.ToString("F1")% ممتلئة</span>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="filament-empty-state">
                <div class="filament-empty-state-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="filament-empty-state-title">لا توجد غرف مسجلة</h3>
                <p class="filament-empty-state-description">ابدأ بإضافة أول غرفة في النظام</p>
                <a href="@Url.Action("Create")" class="filament-button filament-button-primary">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة غرفة جديدة
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        // البحث في الغرف
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const roomCards = document.querySelectorAll('.filament-room-card');
            
            roomCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                card.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // تأثيرات تفاعلية
        document.querySelectorAll('.filament-room-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            });
        });
    </script>
}
