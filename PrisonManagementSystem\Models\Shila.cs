using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PrisonManagementSystem.Models
{
    public class Shila
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم الشيلة")]
        public string Number { get; set; }

        [StringLength(100)]
        [Display(Name = "اسم الشيلة")]
        public string Name { get; set; }

        [StringLength(500)]
        [Display(Name = "وصف الشيلة")]
        public string Description { get; set; }

        [Display(Name = "السعة القصوى")]
        [Range(1, 10)]
        public int MaxCapacity { get; set; } = 1;

        [Display(Name = "العدد الحالي")]
        public int CurrentCount { get; set; } = 0;

        [Display(Name = "نشطة")]
        public bool IsActive { get; set; } = true;

        [Required]
        [StringLength(100)]
        [Display(Name = "نوع العقوبة")]
        public string PunishmentType { get; set; }

        [Display(Name = "مدة العقوبة القصوى (بالأيام)")]
        [Range(1, 365)]
        public int MaxPunishmentDays { get; set; } = 30;

        [StringLength(500)]
        [Display(Name = "شروط الشيلة")]
        public string Conditions { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "آخر تحديث")]
        public DateTime? LastUpdated { get; set; }

        // العلاقة مع الغرفة (الشيلة هي غرفة عقوبة خاصة)
        [Required]
        [Display(Name = "الغرفة")]
        public int RoomId { get; set; }
        [ForeignKey("RoomId")]
        public virtual Room Room { get; set; }

        // العلاقة مع النزلاء المعاقبين
        public virtual ICollection<PrisonerPunishment> PrisonerPunishments { get; set; }
    }
}
