#pragma checksum "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee6003"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Shared__LayoutFilament), @"mvc.1.0.view", @"/Views/Shared/_LayoutFilament.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem

#nullable disable
    ;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee6003", @"/Views/Shared/_LayoutFilament.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6b5cda3430a180463c664dbd4c2b7104438aceea37f75cdb0f39bc7283777da", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Shared__LayoutFilament : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("icon"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("image/svg+xml"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/logo-icon.svg"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("alternate icon"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/favicon.ico"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/bootstrap/dist/css/bootstrap.rtl.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/arabic-typography.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/filament.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/filament-enhanced.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/professional-effects.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/enhanced-forms.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/site.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/filament-theme.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/filament-forms.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/dashboard.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/logo.svg"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString("شعار القيادة العامة للقوات المسلحة العربية الليبية"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("app-logo"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("asp-area", "Identity", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_20 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("asp-page", "/Account/Logout", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_21 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("method", "post", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_22 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("display: inline;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_23 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_24 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_25 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "~/js/site.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_26 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("filament-main"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_ScriptTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<!DOCTYPE html>\n<html lang=\"ar\" dir=\"rtl\">\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600313702", async() => {
                WriteLiteral("\n    <meta charset=\"utf-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>");
                Write(
#nullable restore
#line 6 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
            ViewData["Title"]

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(" - نظام إدارة سجن الكويفية</title>\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600314446", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600315735", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <link rel=""preconnect"" href=""https://fonts.googleapis.com"">
    <link rel=""preconnect"" href=""https://fonts.gstatic.com"" crossorigin>
    <link href=""https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800&display=swap"" rel=""stylesheet"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600317256", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600318457", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600319658", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600320859", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600322060", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600323262", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600324464", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600325666", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600326868", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600328070", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <link rel=""stylesheet"" href=""https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"" />

    <style>
        /* Enhanced Typography */
        body {
            font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-optical-sizing: auto;
            font-variation-settings: ""slnt"" 0;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Enhanced Sidebar */
        .filament-sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .filament-header-brand {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            background: rgba(255, 255, 255, 0.05);
        }

        .filament-nav-item {
            font");
                WriteLiteral(@"-family: 'Cairo', sans-serif !important;
            font-weight: 600 !important;
            font-size: 0.95rem !important;
            color: #ffffff !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            border-radius: 0.75rem !important;
            margin: 0.25rem 0.75rem !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        .filament-nav-item:hover {
            background: rgba(99, 102, 241, 0.15) !important;
            color: #ffffff !important;
            transform: translateX(-4px) !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
        }

        .filament-nav-item.active {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%) !important;
            color: #ffffff !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
            font-weight: 700 !important;
        }

");
                WriteLiteral(@"        .filament-nav-group-label {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 800 !important;
            font-size: 0.8rem !important;
            text-transform: uppercase !important;
            letter-spacing: 0.1em !important;
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6) !important;
            margin-bottom: 0.5rem !important;
        }

        /* Enhanced Navigation Icons */
        .filament-nav-item i {
            color: #ffffff !important;
            font-size: 1rem !important;
            margin-left: 0.75rem !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
            transition: all 0.3s ease !important;
        }

        .filament-nav-item:hover i {
            color: #ffffff !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
            transform: scale(1.1) !important;
        }

        .filament-nav-item.active i {
            color: #ffffff !imp");
                WriteLiteral(@"ortant;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
        }

        /* Enhanced Sidebar Background */
        .filament-sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        /* Enhanced Text Contrast */
        .filament-nav-item,
        .filament-nav-item span,
        .filament-nav-item a {
            color: #ffffff !important;
            text-decoration: none !important;
            font-weight: 600 !important;
        }

        /* Enhanced Button Text */
        .filament-nav-item button {
            color: #ffffff !important;
            font-family: 'Cairo', sans-serif !important;
            font-weight: 600 !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        .filament-nav-item button:hover {
            color: #ffffff !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
 ");
                WriteLiteral(@"       }

        /* Enhanced Navigation Group */
        .filament-nav-group {
            margin-bottom: 1.5rem !important;
        }

        /* Enhanced Brand Text */
        .filament-brand-text {
            color: #ffffff !important;
            font-weight: 700 !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
        }

        /* Additional Text Elements - Force White Text */
        .filament-sidebar *,
        .filament-sidebar a,
        .filament-sidebar span,
        .filament-sidebar div,
        .filament-sidebar button,
        .filament-sidebar .text-white,
        .filament-sidebar .navbar-brand,
        .filament-sidebar .nav-link {
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        /* Override any inherited colors */
        .filament-sidebar .filament-nav-item,
        .filament-sidebar .filament-nav-item *,
        .filament-sidebar .filament-nav-group-label,
        .filament-sidebar .");
                WriteLiteral(@"filament-brand-text,
        .filament-sidebar h1,
        .filament-sidebar h2,
        .filament-sidebar h3,
        .filament-sidebar h4,
        .filament-sidebar h5,
        .filament-sidebar h6,
        .filament-sidebar p {
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        /* Enhanced Main Content */
        .filament-main-content {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
        }

        /* Enhanced Animations */
        ");
                WriteLiteral("@keyframes fadeInUp {\n            from {\n                opacity: 0;\n                transform: translateY(30px);\n            }\n            to {\n                opacity: 1;\n                transform: translateY(0);\n            }\n        }\n\n        ");
                WriteLiteral("@keyframes slideInRight {\n            from {\n                opacity: 0;\n                transform: translateX(30px);\n            }\n            to {\n                opacity: 1;\n                transform: translateX(0);\n            }\n        }\n\n        ");
                WriteLiteral(@"@keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .filament-page > * {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .filament-header {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .filament-stat-card-enhanced:nth-child(1) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both; }
        .filament-stat-card-enhanced:nth-child(2) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both; }
        .filament-stat-card-enhanced:nth-child(3) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both; }
        .filament-stat-card-enhanced:nth-child(4) { animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both; }

        .filament-table-row {
            animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
");
                WriteLiteral(@"
        /* Enhanced Page Transitions */
        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Loading States */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #");
                WriteLiteral("e5e7eb;\n            border-top: 3px solid #6366f1;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n        }\n\n        ");
                WriteLiteral("@keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n        }\n    </style>\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600338910", async() => {
                WriteLiteral("\n    <!-- Sidebar -->\n    <aside class=\"filament-sidebar\">\n        <div class=\"filament-header-brand\" style=\"padding: 24px;\">\n            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600339337", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
            <div>
                <h3 class=""filament-header-title"" style=""font-size: 16px; margin: 0;"">نظام إدارة سجن الكويفية</h3>
                <p style=""font-size: 12px; color: var(--gray-500); margin: 0;"">الكتيبة 210 مشاة آلية</p>
            </div>
        </div>
        
        <nav class=""filament-nav"">
            <div class=""filament-nav-group"">
                <div class=""filament-nav-group-label"">القائمة الرئيسية</div>
                <a");
                BeginWriteAttribute("href", " href=\"", 10529, "\"", 10564, 1);
                WriteAttributeValue("", 10536, 
#nullable restore
#line 293 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Index", "Home")

#line default
#line hidden
#nullable disable
                , 10536, 28, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 10565, "\"", 10739, 2);
                WriteAttributeValue("", 10573, "filament-nav-item", 10573, 17, true);
                WriteAttributeValue(" ", 10590, 
#nullable restore
#line 293 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                  ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 10591, 148, false);
                EndWriteAttribute();
                WriteLiteral(">\n                    <i class=\"fas fa-home\"></i>\n                    الرئيسية\n                </a>\n                <a");
                BeginWriteAttribute("href", " href=\"", 10858, "\"", 10897, 1);
                WriteAttributeValue("", 10865, 
#nullable restore
#line 297 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Dashboard", "Home")

#line default
#line hidden
#nullable disable
                , 10865, 32, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 10898, "\"", 11076, 2);
                WriteAttributeValue("", 10906, "filament-nav-item", 10906, 17, true);
                WriteAttributeValue(" ", 10923, 
#nullable restore
#line 297 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                      ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "Dashboard" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 10924, 152, false);
                EndWriteAttribute();
                WriteLiteral(">\n                    <i class=\"fas fa-tachometer-alt\"></i>\n                    لوحة التحكم\n                </a>\n                <a");
                BeginWriteAttribute("href", " href=\"", 11208, "\"", 11250, 1);
                WriteAttributeValue("", 11215, 
#nullable restore
#line 301 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Control", "Prisoners")

#line default
#line hidden
#nullable disable
                , 11215, 35, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 11251, "\"", 11432, 2);
                WriteAttributeValue("", 11259, "filament-nav-item", 11259, 17, true);
                WriteAttributeValue(" ", 11276, 
#nullable restore
#line 301 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                         ViewContext.RouteData.Values["Controller"]?.ToString() == "Prisoners" && ViewContext.RouteData.Values["Action"]?.ToString() == "Control" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 11277, 155, false);
                EndWriteAttribute();
                WriteLiteral(@">
                    <i class=""fas fa-users""></i>
                    كنترول النزلاء
                </a>
            </div>
            
            <div class=""filament-nav-group"">
                <div class=""filament-nav-group-label"">إدارة النزلاء</div>
                <a");
                BeginWriteAttribute("href", " href=\"", 11709, "\"", 11749, 1);
                WriteAttributeValue("", 11716, 
#nullable restore
#line 309 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Index", "Prisoners")

#line default
#line hidden
#nullable disable
                , 11716, 33, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 11750, "\"", 11929, 2);
                WriteAttributeValue("", 11758, "filament-nav-item", 11758, 17, true);
                WriteAttributeValue(" ", 11775, 
#nullable restore
#line 309 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                       ViewContext.RouteData.Values["Controller"]?.ToString() == "Prisoners" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 11776, 153, false);
                EndWriteAttribute();
                WriteLiteral(">\n                    <i class=\"fas fa-user\"></i>\n                    النزلاء\n                </a>\n                <a");
                BeginWriteAttribute("href", " href=\"", 12047, "\"", 12091, 1);
                WriteAttributeValue("", 12054, 
#nullable restore
#line 313 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Index", "PrisonerCases")

#line default
#line hidden
#nullable disable
                , 12054, 37, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 12092, "\"", 12210, 2);
                WriteAttributeValue("", 12100, "filament-nav-item", 12100, 17, true);
                WriteAttributeValue(" ", 12117, 
#nullable restore
#line 313 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                           ViewContext.RouteData.Values["Controller"]?.ToString() == "PrisonerCases" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 12118, 92, false);
                EndWriteAttribute();
                WriteLiteral(">\n                    <i class=\"fas fa-gavel\"></i>\n                    القضايا\n                </a>\n                <a");
                BeginWriteAttribute("href", " href=\"", 12329, "\"", 12377, 1);
                WriteAttributeValue("", 12336, 
#nullable restore
#line 317 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Index", "PrisonerMovements")

#line default
#line hidden
#nullable disable
                , 12336, 41, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 12378, "\"", 12500, 2);
                WriteAttributeValue("", 12386, "filament-nav-item", 12386, 17, true);
                WriteAttributeValue(" ", 12403, 
#nullable restore
#line 317 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                               ViewContext.RouteData.Values["Controller"]?.ToString() == "PrisonerMovements" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 12404, 96, false);
                EndWriteAttribute();
                WriteLiteral(@">
                    <i class=""fas fa-exchange-alt""></i>
                    الحركات
                </a>
            </div>
            
            <div class=""filament-nav-group"">
                <div class=""filament-nav-group-label"">إدارة المرافق</div>
                <a");
                BeginWriteAttribute("href", " href=\"", 12777, "\"", 12813, 1);
                WriteAttributeValue("", 12784, 
#nullable restore
#line 325 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Index", "Wards")

#line default
#line hidden
#nullable disable
                , 12784, 29, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 12814, "\"", 12924, 2);
                WriteAttributeValue("", 12822, "filament-nav-item", 12822, 17, true);
                WriteAttributeValue(" ", 12839, 
#nullable restore
#line 325 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                   ViewContext.RouteData.Values["Controller"]?.ToString() == "Wards" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 12840, 84, false);
                EndWriteAttribute();
                WriteLiteral(">\n                    <i class=\"fas fa-building\"></i>\n                    العنابر\n                </a>\n                <a");
                BeginWriteAttribute("href", " href=\"", 13046, "\"", 13082, 1);
                WriteAttributeValue("", 13053, 
#nullable restore
#line 329 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Index", "Rooms")

#line default
#line hidden
#nullable disable
                , 13053, 29, false);
                EndWriteAttribute();
                BeginWriteAttribute("class", " class=\"", 13083, "\"", 13193, 2);
                WriteAttributeValue("", 13091, "filament-nav-item", 13091, 17, true);
                WriteAttributeValue(" ", 13108, 
#nullable restore
#line 329 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                   ViewContext.RouteData.Values["Controller"]?.ToString() == "Rooms" ? "active" : ""

#line default
#line hidden
#nullable disable
                , 13109, 84, false);
                EndWriteAttribute();
                WriteLiteral(@">
                    <i class=""fas fa-door-open""></i>
                    الغرف
                </a>
            </div>
            
            <div class=""filament-nav-group"">
                <div class=""filament-nav-group-label"">النظام</div>
                <a");
                BeginWriteAttribute("href", " href=\"", 13458, "\"", 13495, 1);
                WriteAttributeValue("", 13465, 
#nullable restore
#line 337 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                          Url.Action("Privacy", "Home")

#line default
#line hidden
#nullable disable
                , 13465, 30, false);
                EndWriteAttribute();
                WriteLiteral(" class=\"filament-nav-item\">\n                    <i class=\"fas fa-shield-alt\"></i>\n                    الخصوصية\n                </a>\n");
#nullable restore
#line 341 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                 if (User.Identity.IsAuthenticated)
                {

#line default
#line hidden
#nullable disable

                WriteLiteral("                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600352981", async() => {
                    WriteLiteral(@"
                        <button type=""submit"" class=""filament-nav-item"" style=""background: none; border: none; width: 100%; text-align: right;"">
                            <i class=""fas fa-sign-out-alt""></i>
                            تسجيل الخروج
                        </button>
                    ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper.Area = (string)__tagHelperAttribute_19.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_19);
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper.Page = (string)__tagHelperAttribute_20.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_20);
                if (__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper.RouteValues == null)
                {
                    throw new InvalidOperationException(InvalidTagHelperIndexerAssignment("asp-route-returnUrl", "Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper", "RouteValues"));
                }
                BeginWriteTagHelperAttribute();
                WriteLiteral(
#nullable restore
#line 343 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                               Url.Action("Index", "Home")

#line default
#line hidden
#nullable disable
                );
                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper.RouteValues["returnUrl"] = __tagHelperStringValueBuffer;
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-route-returnUrl", __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper.RouteValues["returnUrl"], global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper.Method = (string)__tagHelperAttribute_21.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_21);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_22);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n");
#nullable restore
#line 349 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                }
                else
                {

#line default
#line hidden
#nullable disable

                WriteLiteral("                    <a");
                BeginWriteAttribute("href", " href=\"", 14253, "\"", 14291, 1);
                WriteAttributeValue("", 14260, 
#nullable restore
#line 352 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                              Url.Action("Login", "Account")

#line default
#line hidden
#nullable disable
                , 14260, 31, false);
                EndWriteAttribute();
                WriteLiteral(" class=\"filament-nav-item\">\n                        <i class=\"fas fa-sign-in-alt\"></i>\n                        تسجيل الدخول\n                    </a>\n");
#nullable restore
#line 356 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                }

#line default
#line hidden
#nullable disable

                WriteLiteral(@"            </div>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class=""filament-content"">
        <!-- Header -->
        <header class=""filament-header"">
            <div class=""filament-header-brand"">
                <h1 class=""filament-header-title"">");
                Write(
#nullable restore
#line 366 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                   ViewData["Title"]

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</h1>\n            </div>\n            <div class=\"flex items-center gap-4\">\n");
#nullable restore
#line 369 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                 if (User.Identity.IsAuthenticated)
                {

#line default
#line hidden
#nullable disable

                WriteLiteral("                    <div class=\"flex items-center gap-2\">\n                        <i class=\"fas fa-user-circle\" style=\"font-size: 24px; color: var(--gray-600);\"></i>\n                        <span style=\"color: var(--gray-700); font-weight: 500;\">");
                Write(
#nullable restore
#line 373 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                                                 User.Identity.Name

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</span>\n                    </div>\n");
#nullable restore
#line 375 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                }

#line default
#line hidden
#nullable disable

                WriteLiteral(@"                <button class=""filament-btn filament-btn-secondary d-lg-none"" onclick=""toggleSidebar()"">
                    <i class=""fas fa-bars""></i>
                </button>
            </div>
        </header>

        <!-- Page Content -->
        <div class=""filament-fade-in"">
            ");
                Write(
#nullable restore
#line 384 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
             RenderBody()

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\n        </div>\n    </main>\n\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600360257", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_23);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600361380", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2ded2b201befd25447a16400277df082937a3a02c1d53b0dbd15af9d2aee600362503", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_ScriptTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_ScriptTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_ScriptTagHelper.Src = (string)__tagHelperAttribute_25.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_25);
                __Microsoft_AspNetCore_Mvc_TagHelpers_ScriptTagHelper.AppendVersion = 
#nullable restore
#line 390 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
                                                   true

#line default
#line hidden
#nullable disable
                ;
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-append-version", __Microsoft_AspNetCore_Mvc_TagHelpers_ScriptTagHelper.AppendVersion, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.filament-sidebar');
            sidebar.classList.toggle('open');
        }

        // Enhanced UI Interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Page Load Animation
            document.body.classList.add('page-transition');
            setTimeout(() => document.body.classList.add('loaded'), 100);

            // Enhanced form interactions
            const formInputs = document.querySelectorAll('input, textarea, select');
            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.closest('.filament-form-field')?.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.closest('.filament-form-field')?.classList.remove('focused');
                });
            });

            // Enhanced button ripple effect
   ");
                WriteLiteral(@"         const buttons = document.querySelectorAll('.filament-button, .btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.3);
                        transform: scale(0);
                        animation: ripple-animation 0.6s linear;
                        pointer-events: none;
      ");
                WriteLiteral(@"              `;

                    this.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 600);
                });
            });

            // Enhanced table interactions
            const tableRows = document.querySelectorAll('.filament-table-row');
            tableRows.forEach((row, index) => {
                row.style.animationDelay = `${index * 0.05}s`;
            });

            // Enhanced search functionality
            const searchInputs = document.querySelectorAll('.filament-search-field');
            searchInputs.forEach(input => {
                let searchTimeout;
                input.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = this.value.toLowerCase();

                    searchTimeout = setTimeout(() => {
                        const searchableElements = document.querySelectorAll('.filament-table-row, .filament-ward-card, .filament-room-card');
                        sear");
                WriteLiteral(@"chableElements.forEach(element => {
                            const isVisible = element.textContent.toLowerCase().includes(searchTerm);
                            element.style.display = isVisible ? '' : 'none';
                        });
                    }, 300);
                });
            });
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.filament-sidebar');
            const toggleBtn = event.target.closest('[onclick=""toggleSidebar()""]');

            if (!sidebar.contains(event.target) && !toggleBtn && window.innerWidth <= 1024) {
                sidebar.classList.remove('open');
            }
        });
    </script>

    <style>
        ");
                WriteLiteral(@"@keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
    
    ");
                Write(
#nullable restore
#line 500 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Shared\_LayoutFilament.cshtml"
     await RenderSectionAsync("Scripts", required: false)

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_26);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n</html>\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
