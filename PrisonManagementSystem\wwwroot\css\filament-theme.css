/* <PERSON>vel Filament Theme for Prison Management System */

:root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --danger-500: #ef4444;
    
    --military-gold: #D4AF37;
    --military-green: #228B22;
    --military-brown: #8B4513;
}

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    direction: rtl;
}

/* Logo Styles */
.app-logo {
    width: 60px;
    height: 60px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.app-logo-large {
    width: 120px;
    height: 120px;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.15));
}

.military-brand img {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* Layout Components */
.filament-main {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.filament-sidebar {
    width: 280px;
    background: white;
    border-left: 1px solid var(--gray-200);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 40;
}

.filament-content {
    margin-right: 280px;
    padding: 24px;
}

/* Header */
.filament-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.filament-header-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.filament-header-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

/* Navigation */
.filament-nav {
    padding: 24px 0;
}

.filament-nav-group {
    margin-bottom: 24px;
}

.filament-nav-group-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 24px 8px;
}

.filament-nav-item {
    display: block;
    padding: 12px 24px;
    color: var(--gray-700);
    text-decoration: none;
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.filament-nav-item:hover {
    background-color: var(--gray-50);
    color: var(--primary-600);
}

.filament-nav-item.active {
    background-color: var(--primary-50);
    color: var(--primary-600);
    border-right-color: var(--primary-600);
    font-weight: 600;
}

.filament-nav-item i {
    width: 20px;
    margin-left: 12px;
    text-align: center;
}

/* Cards */
.filament-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.filament-card-header {
    padding: 24px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.filament-card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filament-card-content {
    padding: 24px;
}

/* Buttons */
.filament-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.filament-btn-primary {
    background-color: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
}

.filament-btn-primary:hover {
    background-color: var(--primary-700);
    border-color: var(--primary-700);
    color: white;
}

.filament-btn-secondary {
    background-color: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.filament-btn-secondary:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
}

.filament-btn-success {
    background-color: var(--success-500);
    color: white;
    border-color: var(--success-500);
}

.filament-btn-warning {
    background-color: var(--warning-500);
    color: white;
    border-color: var(--warning-500);
}

.filament-btn-danger {
    background-color: var(--danger-500);
    color: white;
    border-color: var(--danger-500);
}

.filament-btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.filament-btn-lg {
    padding: 14px 20px;
    font-size: 16px;
}

/* Forms */
.filament-form-group {
    margin-bottom: 20px;
}

.filament-label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 6px;
    font-size: 14px;
}

.filament-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: white;
}

.filament-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filament-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-left: 40px;
    appearance: none;
}

/* Tables */
.filament-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.filament-table th {
    background-color: var(--gray-50);
    padding: 16px;
    text-align: right;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    font-size: 14px;
}

.filament-table td {
    padding: 16px;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-900);
    font-size: 14px;
}

.filament-table tbody tr:hover {
    background-color: var(--gray-50);
}

.filament-table tbody tr:last-child td {
    border-bottom: none;
}

/* Badges */
.filament-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.filament-badge-success {
    background-color: #dcfce7;
    color: #166534;
}

.filament-badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.filament-badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.filament-badge-info {
    background-color: var(--primary-50);
    color: var(--primary-700);
}

/* Stats */
.filament-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
}

.filament-stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
}

.filament-stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.filament-stat-label {
    font-size: 14px;
    color: var(--gray-600);
    font-weight: 500;
}

.filament-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.filament-stat-icon.primary {
    background-color: var(--primary-100);
    color: var(--primary-600);
}

.filament-stat-icon.success {
    background-color: #dcfce7;
    color: var(--success-500);
}

.filament-stat-icon.warning {
    background-color: #fef3c7;
    color: var(--warning-500);
}

.filament-stat-icon.danger {
    background-color: #fee2e2;
    color: var(--danger-500);
}

/* Search and Filters */
.filament-search-container {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.filament-search-input {
    flex: 1;
    min-width: 300px;
}

.filament-filter-container {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Pagination */
.filament-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
}

.filament-pagination-btn {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-700);
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.filament-pagination-btn:hover {
    background-color: var(--gray-50);
}

.filament-pagination-btn.active {
    background-color: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
}

/* Military Theme Enhancements */
.military-header {
    background: linear-gradient(135deg, var(--military-gold) 0%, #B8860B 100%);
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.military-accent {
    border-right: 4px solid var(--military-gold);
}

.military-badge {
    background: linear-gradient(135deg, var(--military-gold) 0%, #B8860B 100%);
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Loading States */
.filament-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--gray-200);
    border-radius: 50%;
    border-top-color: var(--primary-600);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.filament-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .filament-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .filament-sidebar.open {
        transform: translateX(0);
    }

    .filament-content {
        margin-right: 0;
    }

    .filament-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .filament-content {
        padding: 16px;
    }

    .filament-search-container {
        flex-direction: column;
    }

    .filament-search-input {
        min-width: auto;
    }

    .filament-stats {
        grid-template-columns: 1fr;
    }

    .filament-table {
        font-size: 12px;
    }

    .filament-table th,
    .filament-table td {
        padding: 12px 8px;
    }
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-4 { margin-bottom: 16px; }
.mb-6 { margin-bottom: 24px; }
.mt-4 { margin-top: 16px; }
.mt-6 { margin-top: 24px; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-4 { gap: 16px; }
.w-full { width: 100%; }
.hidden { display: none; }
