using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PrisonManagementSystem.Models
{
    public class Ward
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم العنبر")]
        public string Name { get; set; }

        [StringLength(200)]
        [Display(Name = "الوصف")]
        public string Description { get; set; }

        [Display(Name = "السعة القصوى")]
        public int MaxCapacity { get; set; }

        [Display(Name = "العدد الحالي")]
        public int CurrentCount { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // العلاقات
        public virtual ICollection<Room> Rooms { get; set; } = new List<Room>();
        public virtual ICollection<Prisoner> Prisoners { get; set; } = new List<Prisoner>();
    }
}
