-- سكريبت إنشاء قاعدة بيانات نظام إدارة سجن الكويفية
-- Prison Management System Database Creation Script

USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PrisonManagementDB')
BEGIN
    CREATE DATABASE PrisonManagementDB;
END
GO

USE PrisonManagementDB;
GO

-- جدول العنابر (Wards)
CREATE TABLE Wards (
    Id int IDENTITY(1,1) PRIMARY KEY,
    Name nvarchar(50) NOT NULL,
    Description nvarchar(200),
    MaxCapacity int NOT NULL,
    CurrentCount int NOT NULL DEFAULT 0,
    IsActive bit NOT NULL DEFAULT 1
);

-- جدول الغرف (Rooms)
CREATE TABLE Rooms (
    Id int IDENTITY(1,1) PRIMARY KEY,
    RoomNumber nvarchar(50) NOT NULL,
    Description nvarchar(200),
    MaxCapacity int NOT NULL,
    CurrentCount int NOT NULL DEFAULT 0,
    IsActive bit NOT NULL DEFAULT 1,
    WardId int NOT NULL,
    FOREIGN KEY (WardId) REFERENCES Wards(Id)
);

-- جدول الشيل (Shilas)
CREATE TABLE Shilas (
    Id int IDENTITY(1,1) PRIMARY KEY,
    Name nvarchar(50) NOT NULL,
    Description nvarchar(200),
    MaxCapacity int NOT NULL,
    CurrentCount int NOT NULL DEFAULT 0,
    IsActive bit NOT NULL DEFAULT 1,
    RoomId int NOT NULL,
    FOREIGN KEY (RoomId) REFERENCES Rooms(Id)
);

-- جدول النزلاء (Prisoners)
CREATE TABLE Prisoners (
    Id int IDENTITY(1,1) PRIMARY KEY,
    PrisonerNumber nvarchar(20) NOT NULL UNIQUE,
    
    -- البيانات الشخصية
    FullName nvarchar(100) NOT NULL,
    MotherName nvarchar(50) NOT NULL,
    DateOfBirth datetime2 NOT NULL,
    PlaceOfBirth nvarchar(50) NOT NULL,
    Nationality nvarchar(50) NOT NULL,
    PassportNumber nvarchar(20),
    IdCardNumber nvarchar(20),
    NationalNumber nvarchar(20) NOT NULL UNIQUE,
    
    -- البيانات الصحية
    HealthStatus nvarchar(200),
    ChronicDiseases nvarchar(500),
    DisabilityPercentage decimal(5,2),
    
    -- بيانات السجن
    EntryDate datetime2 NOT NULL,
    HasPreviousImprisonment bit NOT NULL DEFAULT 0,
    ExpectedReleaseDate datetime2,
    TotalSentenceDays int,
    
    -- الموقع
    WardId int,
    RoomId int,
    
    -- تواريخ النظام
    CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedDate datetime2,
    CreatedBy nvarchar(50),
    UpdatedBy nvarchar(50),
    
    FOREIGN KEY (WardId) REFERENCES Wards(Id),
    FOREIGN KEY (RoomId) REFERENCES Rooms(Id)
);

-- جدول قضايا النزلاء (PrisonerCases)
CREATE TABLE PrisonerCases (
    Id int IDENTITY(1,1) PRIMARY KEY,
    CaseNumber nvarchar(50) NOT NULL,
    Charge nvarchar(200) NOT NULL,
    ChargeDetails nvarchar(500),
    Verdict nvarchar(100),
    SentenceDays int,
    Fine decimal(18,2),
    SentenceStartDate datetime2,
    SentenceEndDate datetime2,
    VerdictStatus nvarchar(20) NOT NULL, -- محكوم، موقوف، مبرأ
    CaseDate datetime2 NOT NULL,
    Court nvarchar(100),
    Judge nvarchar(100),
    Notes nvarchar(500),
    IsActive bit NOT NULL DEFAULT 1,
    
    -- العلاقة مع السجين
    PrisonerId int NOT NULL,
    
    -- تواريخ النظام
    CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedDate datetime2,
    CreatedBy nvarchar(50),
    UpdatedBy nvarchar(50),
    
    FOREIGN KEY (PrisonerId) REFERENCES Prisoners(Id) ON DELETE CASCADE
);

-- جدول أنواع الحركات (MovementTypes)
CREATE TABLE MovementTypes (
    Id int IDENTITY(1,1) PRIMARY KEY,
    Name nvarchar(50) NOT NULL,
    Description nvarchar(200),
    Category nvarchar(20) NOT NULL, -- داخلي، خارجي
    RequiresReturnDate bit NOT NULL DEFAULT 0,
    RequiresApproval bit NOT NULL DEFAULT 0,
    IsActive bit NOT NULL DEFAULT 1,
    DisplayOrder int NOT NULL DEFAULT 0
);

-- جدول حركات النزلاء (PrisonerMovements)
CREATE TABLE PrisonerMovements (
    Id int IDENTITY(1,1) PRIMARY KEY,
    MovementType nvarchar(50) NOT NULL,
    MovementCategory nvarchar(100) NOT NULL,
    MovementDate datetime2 NOT NULL,
    ExpectedReturnDate datetime2,
    ActualReturnDate datetime2,
    Destination nvarchar(200),
    Reason nvarchar(500),
    Notes nvarchar(500),
    Status nvarchar(20) NOT NULL, -- نشطة، مكتملة، ملغية
    
    -- بيانات النقل الداخلي
    FromWardId int,
    FromRoomId int,
    ToWardId int,
    ToRoomId int,
    
    -- العلاقة مع السجين
    PrisonerId int NOT NULL,
    
    -- تواريخ النظام
    CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
    CreatedBy nvarchar(50),
    ApprovedBy nvarchar(50),
    ApprovedDate datetime2,
    
    FOREIGN KEY (PrisonerId) REFERENCES Prisoners(Id) ON DELETE CASCADE,
    FOREIGN KEY (FromWardId) REFERENCES Wards(Id),
    FOREIGN KEY (FromRoomId) REFERENCES Rooms(Id),
    FOREIGN KEY (ToWardId) REFERENCES Wards(Id),
    FOREIGN KEY (ToRoomId) REFERENCES Rooms(Id)
);

-- إدراج البيانات الأولية

-- إدراج أنواع الحركات
INSERT INTO MovementTypes (Name, Description, Category, RequiresReturnDate, RequiresApproval, DisplayOrder) VALUES
('إفراج', 'إفراج نهائي', 'خارجي', 0, 1, 1),
('تشغيل خارجي', 'تشغيل خارج السجن', 'خارجي', 1, 1, 2),
('مأمورية', 'مأمورية رسمية', 'خارجي', 1, 1, 3),
('نقل داخلي', 'نقل بين العنابر والغرف', 'داخلي', 0, 0, 4),
('علاج خارجي', 'علاج في مستشفى خارجي', 'خارجي', 1, 1, 5);

-- إدراج العنابر الأساسية
INSERT INTO Wards (Name, Description, MaxCapacity, CurrentCount) VALUES
('العنبر الأول', 'عنبر الجنايات', 50, 0),
('العنبر الثاني', 'عنبر الجنح', 40, 0),
('العنبر الثالث', 'عنبر الموقوفين', 30, 0);

-- إدراج غرف أساسية
INSERT INTO Rooms (RoomNumber, Description, MaxCapacity, CurrentCount, WardId) VALUES
('101', 'غرفة 101 - العنبر الأول', 10, 0, 1),
('102', 'غرفة 102 - العنبر الأول', 10, 0, 1),
('201', 'غرفة 201 - العنبر الثاني', 8, 0, 2),
('202', 'غرفة 202 - العنبر الثاني', 8, 0, 2),
('301', 'غرفة 301 - العنبر الثالث', 6, 0, 3),
('302', 'غرفة 302 - العنبر الثالث', 6, 0, 3);

-- إنشاء الفهارس
CREATE INDEX IX_Prisoners_PrisonerNumber ON Prisoners(PrisonerNumber);
CREATE INDEX IX_Prisoners_NationalNumber ON Prisoners(NationalNumber);
CREATE INDEX IX_PrisonerCases_CaseNumber ON PrisonerCases(CaseNumber);
CREATE INDEX IX_PrisonerCases_PrisonerId ON PrisonerCases(PrisonerId);
CREATE INDEX IX_PrisonerMovements_PrisonerId ON PrisonerMovements(PrisonerId);
CREATE INDEX IX_Rooms_WardId ON Rooms(WardId);
CREATE INDEX IX_Shilas_RoomId ON Shilas(RoomId);

PRINT 'تم إنشاء قاعدة بيانات نظام إدارة سجن الكويفية بنجاح!';
PRINT 'Prison Management Database Created Successfully!';
GO
