-- سكريبت إنشاء جداول المستخدمين والأدوار (ASP.NET Identity)
-- Identity Tables Creation Script

USE PrisonManagementDB;
GO

-- جدول الأدوار (AspNetRoles)
CREATE TABLE AspNetRoles (
    Id nvarchar(450) NOT NULL PRIMARY KEY,
    Name nvarchar(256),
    NormalizedName nvarchar(256),
    ConcurrencyStamp nvarchar(max)
);

-- جدول المستخدمين (AspNetUsers)
CREATE TABLE AspNetUsers (
    Id nvarchar(450) NOT NULL PRIMARY KEY,
    UserName nvarchar(256),
    NormalizedUserName nvarchar(256),
    Email nvarchar(256),
    NormalizedEmail nvarchar(256),
    EmailConfirmed bit NOT NULL,
    PasswordHash nvarchar(max),
    SecurityStamp nvarchar(max),
    ConcurrencyStamp nvarchar(max),
    PhoneNumber nvarchar(max),
    PhoneNumberConfirmed bit NOT NULL,
    TwoFactorEnabled bit NOT NULL,
    LockoutEnd datetimeoffset,
    LockoutEnabled bit NOT NULL,
    AccessFailedCount int NOT NULL,
    
    -- الحقول المخصصة
    FullName nvarchar(100) NOT NULL,
    EmployeeNumber nvarchar(50),
    Position nvarchar(100),
    Department nvarchar(100),
    HireDate datetime2,
    IsActive bit NOT NULL DEFAULT 1,
    CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
    LastLoginDate datetime2,
    Notes nvarchar(500)
);

-- جدول ربط المستخدمين بالأدوار (AspNetUserRoles)
CREATE TABLE AspNetUserRoles (
    UserId nvarchar(450) NOT NULL,
    RoleId nvarchar(450) NOT NULL,
    PRIMARY KEY (UserId, RoleId),
    FOREIGN KEY (UserId) REFERENCES AspNetUsers(Id) ON DELETE CASCADE,
    FOREIGN KEY (RoleId) REFERENCES AspNetRoles(Id) ON DELETE CASCADE
);

-- جدول مطالبات المستخدمين (AspNetUserClaims)
CREATE TABLE AspNetUserClaims (
    Id int IDENTITY(1,1) PRIMARY KEY,
    UserId nvarchar(450) NOT NULL,
    ClaimType nvarchar(max),
    ClaimValue nvarchar(max),
    FOREIGN KEY (UserId) REFERENCES AspNetUsers(Id) ON DELETE CASCADE
);

-- جدول مطالبات الأدوار (AspNetRoleClaims)
CREATE TABLE AspNetRoleClaims (
    Id int IDENTITY(1,1) PRIMARY KEY,
    RoleId nvarchar(450) NOT NULL,
    ClaimType nvarchar(max),
    ClaimValue nvarchar(max),
    FOREIGN KEY (RoleId) REFERENCES AspNetRoles(Id) ON DELETE CASCADE
);

-- جدول تسجيلات الدخول الخارجية (AspNetUserLogins)
CREATE TABLE AspNetUserLogins (
    LoginProvider nvarchar(450) NOT NULL,
    ProviderKey nvarchar(450) NOT NULL,
    ProviderDisplayName nvarchar(max),
    UserId nvarchar(450) NOT NULL,
    PRIMARY KEY (LoginProvider, ProviderKey),
    FOREIGN KEY (UserId) REFERENCES AspNetUsers(Id) ON DELETE CASCADE
);

-- جدول رموز المستخدمين (AspNetUserTokens)
CREATE TABLE AspNetUserTokens (
    UserId nvarchar(450) NOT NULL,
    LoginProvider nvarchar(450) NOT NULL,
    Name nvarchar(450) NOT NULL,
    Value nvarchar(max),
    PRIMARY KEY (UserId, LoginProvider, Name),
    FOREIGN KEY (UserId) REFERENCES AspNetUsers(Id) ON DELETE CASCADE
);

-- إنشاء الفهارس
CREATE UNIQUE INDEX IX_AspNetRoles_NormalizedName ON AspNetRoles(NormalizedName) WHERE NormalizedName IS NOT NULL;
CREATE INDEX IX_AspNetRoleClaims_RoleId ON AspNetRoleClaims(RoleId);
CREATE INDEX IX_AspNetUserClaims_UserId ON AspNetUserClaims(UserId);
CREATE INDEX IX_AspNetUserLogins_UserId ON AspNetUserLogins(UserId);
CREATE INDEX IX_AspNetUserRoles_RoleId ON AspNetUserRoles(RoleId);
CREATE UNIQUE INDEX IX_AspNetUsers_NormalizedUserName ON AspNetUsers(NormalizedUserName) WHERE NormalizedUserName IS NOT NULL;
CREATE INDEX IX_AspNetUsers_NormalizedEmail ON AspNetUsers(NormalizedEmail);

-- إدراج الأدوار الأساسية
INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp) VALUES
(NEWID(), 'Admin', 'ADMIN', NEWID()),
(NEWID(), 'Manager', 'MANAGER', NEWID()),
(NEWID(), 'Officer', 'OFFICER', NEWID()),
(NEWID(), 'User', 'USER', NEWID());

-- إدراج المستخدم الافتراضي (admin)
-- كلمة المرور: Admin123! (مشفرة)
DECLARE @AdminUserId NVARCHAR(450) = NEWID();
DECLARE @AdminRoleId NVARCHAR(450) = (SELECT Id FROM AspNetRoles WHERE Name = 'Admin');

INSERT INTO AspNetUsers (
    Id, UserName, NormalizedUserName, Email, NormalizedEmail, EmailConfirmed,
    PasswordHash, SecurityStamp, ConcurrencyStamp, PhoneNumberConfirmed,
    TwoFactorEnabled, LockoutEnabled, AccessFailedCount,
    FullName, EmployeeNumber, Position, Department, HireDate, IsActive, CreatedDate
) VALUES (
    @AdminUserId, 'admin', 'ADMIN', '<EMAIL>', '<EMAIL>', 1,
    'AQAAAAEAACcQAAAAEJ4+2+8QJ4+2+8QJ4+2+8QJ4+2+8QJ4+2+8QJ4+2+8QJ4+2+8QJ4+2+8QJ4+2+8Q==',
    NEWID(), NEWID(), 0, 0, 1, 0,
    'مدير النظام', '001', 'مدير النظام', 'تقنية المعلومات', GETDATE(), 1, GETDATE()
);

-- ربط المستخدم الافتراضي بدور Admin
INSERT INTO AspNetUserRoles (UserId, RoleId) VALUES (@AdminUserId, @AdminRoleId);

PRINT 'تم إنشاء جداول المستخدمين والأدوار بنجاح!';
PRINT 'Identity Tables Created Successfully!';
PRINT 'المستخدم الافتراضي: admin';
PRINT 'كلمة المرور: Admin123!';
GO
