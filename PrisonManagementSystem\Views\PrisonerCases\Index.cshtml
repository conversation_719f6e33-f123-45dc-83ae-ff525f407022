@model IEnumerable<PrisonManagementSystem.Models.PrisonerCase>

@{
    ViewData["Title"] = "إدارة القضايا";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="filament-page">
    <!-- Header Section -->
    <div class="filament-header">
        <div class="filament-header-content">
            <div class="filament-header-heading">
                <h1 class="filament-header-title">
                    <svg class="filament-header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    إدارة القضايا
                </h1>
                <p class="filament-header-subtitle">إدارة ومتابعة جميع القضايا القانونية للنزلاء</p>
            </div>
            <div class="filament-header-actions">
                <a href="@Url.Action("Create")" class="filament-button filament-button-primary">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة قضية جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="filament-stats-grid">
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-blue">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Count()</div>
                    <div class="filament-stats-label">إجمالي القضايا</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-green">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Count(c => c.IsActive)</div>
                    <div class="filament-stats-label">قضايا نشطة</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-yellow">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Count(c => c.SentenceEndDate.HasValue && c.SentenceEndDate.Value > DateTime.Now)</div>
                    <div class="filament-stats-label">قضايا جارية</div>
                </div>
            </div>
        </div>
        
        <div class="filament-stats-card">
            <div class="filament-stats-content">
                <div class="filament-stats-icon filament-stats-icon-red">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </div>
                <div class="filament-stats-details">
                    <div class="filament-stats-value">@Model.Count(c => c.SentenceEndDate.HasValue && c.SentenceEndDate.Value <= DateTime.Now)</div>
                    <div class="filament-stats-label">قضايا منتهية</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="filament-card">
        <div class="filament-card-header">
            <div class="filament-card-header-content">
                <h3 class="filament-card-title">قائمة القضايا</h3>
                <p class="filament-card-description">إدارة ومتابعة القضايا القانونية</p>
            </div>
            <div class="filament-card-header-actions">
                <div class="filament-search-input">
                    <svg class="filament-search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" placeholder="البحث في القضايا..." class="filament-search-field" id="searchInput">
                </div>
            </div>
        </div>

        @if (Model.Any())
        {
            <div class="filament-table-container">
                <table class="filament-table">
                    <thead class="filament-table-header">
                        <tr>
                            <th class="filament-table-header-cell">رقم القضية</th>
                            <th class="filament-table-header-cell">النزيل</th>
                            <th class="filament-table-header-cell">نوع القضية</th>
                            <th class="filament-table-header-cell">المحكمة</th>
                            <th class="filament-table-header-cell">تاريخ القضية</th>
                            <th class="filament-table-header-cell">نوع الحكم</th>
                            <th class="filament-table-header-cell">مدة الحكم</th>
                            <th class="filament-table-header-cell">الحالة</th>
                            <th class="filament-table-header-cell">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="filament-table-body">
                        @foreach (var item in Model)
                        {
                            <tr class="filament-table-row">
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">
                                        <span class="filament-badge filament-badge-primary">@item.CaseNumber</span>
                                    </div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">
                                        <div class="filament-table-cell-primary">@item.Prisoner?.FullName</div>
                                        <div class="filament-table-cell-secondary">@item.Prisoner?.PrisonerNumber</div>
                                    </div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@item.CaseType</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">
                                        <div class="filament-table-cell-primary">@item.CourtName</div>
                                        <div class="filament-table-cell-secondary">@item.JudgeName</div>
                                    </div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@item.CaseDate.ToString("yyyy/MM/dd")</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@item.SentenceType</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">@item.SentenceDuration</div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-cell-content">
                                        @if (item.IsActive)
                                        {
                                            @if (item.SentenceEndDate.HasValue && item.SentenceEndDate.Value <= DateTime.Now)
                                            {
                                                <span class="filament-badge filament-badge-danger">منتهية</span>
                                            }
                                            else
                                            {
                                                <span class="filament-badge filament-badge-success">نشطة</span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="filament-badge filament-badge-warning">غير نشطة</span>
                                        }
                                    </div>
                                </td>
                                <td class="filament-table-cell">
                                    <div class="filament-table-actions">
                                        <a href="@Url.Action("Details", new { id = item.Id })" class="filament-table-action filament-table-action-view" title="عرض التفاصيل">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </a>
                                        <a href="@Url.Action("Edit", new { id = item.Id })" class="filament-table-action filament-table-action-edit" title="تعديل">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                        <a href="@Url.Action("Delete", new { id = item.Id })" class="filament-table-action filament-table-action-delete" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذه القضية؟')">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="filament-empty-state">
                <div class="filament-empty-state-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="filament-empty-state-title">لا توجد قضايا مسجلة</h3>
                <p class="filament-empty-state-description">ابدأ بإضافة أول قضية في النظام</p>
                <a href="@Url.Action("Create")" class="filament-button filament-button-primary">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة قضية جديدة
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        // البحث في القضايا
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('.filament-table-row');
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // تأثيرات تفاعلية
        document.querySelectorAll('.filament-table-row').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
}
