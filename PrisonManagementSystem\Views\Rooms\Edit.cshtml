@model PrisonManagementSystem.Models.Room

@{
    ViewData["Title"] = "تعديل الغرفة";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="filament-page filament-typography-enhanced">
    <!-- Header Section -->
    <div class="filament-header filament-header-enhanced">
        <div class="filament-header-content">
            <div class="filament-header-heading">
                <h1 class="filament-title-enhanced">
                    <svg class="filament-header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    تعديل الغرفة: @Model.RoomNumber
                </h1>
                <p class="filament-subtitle-enhanced">تعديل بيانات الغرفة في العنبر</p>
            </div>
            <div class="filament-header-actions">
                <a href="@Url.Action("Index")" class="filament-button filament-button-secondary filament-button-enhanced">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <form asp-action="Edit" class="filament-form filament-form-enhanced">
        <div asp-validation-summary="ModelOnly" class="filament-notification filament-notification-danger"></div>
        <input type="hidden" asp-for="Id" />
        
        <!-- معلومات الغرفة -->
        <div class="filament-section">
            <div class="filament-section-header">
                <div class="filament-section-header-content">
                    <h2 class="filament-section-title">
                        <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        معلومات الغرفة
                    </h2>
                    <p class="filament-section-description">تعديل البيانات الأساسية للغرفة</p>
                </div>
            </div>
            
            <div class="filament-section-content">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="filament-form-field">
                            <label asp-for="RoomNumber" class="filament-form-label required">رقم الغرفة *</label>
                            <input asp-for="RoomNumber" class="filament-form-input" placeholder="أدخل رقم الغرفة" required />
                            <span asp-validation-for="RoomNumber" class="filament-form-error"></span>
                            <p class="filament-form-hint">رقم فريد للغرفة داخل العنبر</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="filament-form-field">
                            <label asp-for="WardId" class="filament-form-label required">العنبر *</label>
                            <select asp-for="WardId" class="filament-form-select" asp-items="ViewBag.WardId" required>
                                <option value="">-- اختر العنبر --</option>
                            </select>
                            <span asp-validation-for="WardId" class="filament-form-error"></span>
                            <p class="filament-form-hint">العنبر الذي تنتمي إليه الغرفة</p>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="filament-form-field">
                            <label asp-for="MaxCapacity" class="filament-form-label required">السعة القصوى *</label>
                            <input asp-for="MaxCapacity" class="filament-form-input" type="number" min="1" max="50" placeholder="عدد النزلاء" required />
                            <span asp-validation-for="MaxCapacity" class="filament-form-error"></span>
                            <p class="filament-form-hint">الحد الأقصى لعدد النزلاء</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="filament-form-field">
                            <label asp-for="CurrentCount" class="filament-form-label">العدد الحالي</label>
                            <input asp-for="CurrentCount" class="filament-form-input" type="number" min="0" placeholder="العدد الحالي" readonly />
                            <span asp-validation-for="CurrentCount" class="filament-form-error"></span>
                            <p class="filament-form-hint">العدد الحالي للنزلاء (يتم تحديثه تلقائياً)</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="filament-form-field">
                            <div class="filament-form-checkbox">
                                <input asp-for="IsActive" class="filament-form-checkbox-input" type="checkbox" />
                                <label asp-for="IsActive" class="filament-form-checkbox-label">
                                    <span class="filament-form-checkbox-indicator"></span>
                                    غرفة نشطة
                                </label>
                            </div>
                            <span asp-validation-for="IsActive" class="filament-form-error"></span>
                            <p class="filament-form-hint">هل الغرفة متاحة للاستخدام</p>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filament-form-field">
                            <label asp-for="Description" class="filament-form-label">الوصف</label>
                            <textarea asp-for="Description" class="filament-form-textarea" rows="4" placeholder="وصف الغرفة ومميزاتها (اختياري)"></textarea>
                            <span asp-validation-for="Description" class="filament-form-error"></span>
                            <p class="filament-form-hint">وصف مختصر للغرفة ومميزاتها</p>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الغرفة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filament-stats-mini-grid">
                            <div class="filament-stats-mini-card">
                                <div class="filament-stats-mini-icon filament-stats-icon-blue">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div class="filament-stats-mini-details">
                                    <div class="filament-stats-mini-value">@Model.CurrentCount / @Model.MaxCapacity</div>
                                    <div class="filament-stats-mini-label">الإشغال الحالي</div>
                                </div>
                            </div>
                            
                            <div class="filament-stats-mini-card">
                                <div class="filament-stats-mini-icon filament-stats-icon-green">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="filament-stats-mini-details">
                                    @{
                                        var occupancyPercentage = Model.MaxCapacity > 0 ? (Model.CurrentCount * 100.0 / Model.MaxCapacity) : 0;
                                    }
                                    <div class="filament-stats-mini-value">@occupancyPercentage.ToString("F1")%</div>
                                    <div class="filament-stats-mini-label">نسبة الإشغال</div>
                                </div>
                            </div>
                            
                            <div class="filament-stats-mini-card">
                                <div class="filament-stats-mini-icon filament-stats-icon-purple">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <div class="filament-stats-mini-details">
                                    <div class="filament-stats-mini-value">@(Model.MaxCapacity - Model.CurrentCount)</div>
                                    <div class="filament-stats-mini-label">أماكن متاحة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="filament-form-actions">
            <button type="submit" class="filament-button filament-button-primary filament-button-enhanced">
                <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                حفظ التعديلات
            </button>
            <a href="@Url.Action("Index")" class="filament-button filament-button-secondary filament-button-enhanced">
                <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                إلغاء
            </a>
            <a href="@Url.Action("Delete", new { id = Model.Id })" class="filament-button filament-button-danger filament-button-enhanced">
                <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                حذف الغرفة
            </a>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات تفاعلية للحقول
            const formFields = document.querySelectorAll('.filament-form-input, .filament-form-select, .filament-form-textarea');
            formFields.forEach(field => {
                field.addEventListener('focus', function() {
                    this.closest('.filament-form-field')?.classList.add('focused');
                });

                field.addEventListener('blur', function() {
                    this.closest('.filament-form-field')?.classList.remove('focused');
                });
            });

            // معالجة إرسال النموذج
            const form = document.querySelector('.filament-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitButton = form.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = `
                            <svg class="filament-button-icon animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            جاري الحفظ...
                        `;
                    }
                });
            }
        });
    </script>
}
