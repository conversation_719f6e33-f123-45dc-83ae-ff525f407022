using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PrisonManagementSystem.Models
{
    public class MovementType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم نوع الحركة")]
        public string Name { get; set; }

        [StringLength(200)]
        [Display(Name = "الوصف")]
        public string Description { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "التصنيف")]
        public string Category { get; set; } // داخلي، خارجي

        [Display(Name = "يتطلب تاريخ عودة")]
        public bool RequiresReturnDate { get; set; }

        [Display(Name = "يتطلب اعتماد")]
        public bool RequiresApproval { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; }
    }
}
