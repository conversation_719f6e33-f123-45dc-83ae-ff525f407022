@model PrisonManagementSystem.Models.Room

@{
    ViewData["Title"] = "إضافة غرفة جديدة";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="filament-page filament-typography-enhanced">
    <!-- Header Section -->
    <div class="filament-header filament-header-enhanced">
        <div class="filament-header-content">
            <div class="filament-header-heading">
                <h1 class="filament-title-enhanced">
                    <svg class="filament-header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة غرفة جديدة
                </h1>
                <p class="filament-subtitle-enhanced">إضافة غرفة جديدة في أحد العنابر</p>
            </div>
            <div class="filament-header-actions">
                <a href="@Url.Action("Index")" class="filament-button filament-button-secondary filament-button-enhanced">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <form asp-action="Create" class="filament-form filament-form-enhanced">
        <div asp-validation-summary="ModelOnly" class="filament-notification filament-notification-danger"></div>
        
        <!-- معلومات الغرفة -->
        <div class="filament-section">
            <div class="filament-section-header">
                <div class="filament-section-header-content">
                    <h2 class="filament-section-title">
                        <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        معلومات الغرفة
                    </h2>
                    <p class="filament-section-description">البيانات الأساسية للغرفة الجديدة</p>
                </div>
            </div>
            
            <div class="filament-section-content">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="filament-form-field">
                            <label asp-for="RoomNumber" class="filament-form-label required">رقم الغرفة *</label>
                            <input asp-for="RoomNumber" class="filament-form-input" placeholder="أدخل رقم الغرفة" required />
                            <span asp-validation-for="RoomNumber" class="filament-form-error"></span>
                            <p class="filament-form-hint">رقم فريد للغرفة داخل العنبر</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="filament-form-field">
                            <label asp-for="WardId" class="filament-form-label required">العنبر *</label>
                            <select asp-for="WardId" class="filament-form-select" asp-items="ViewBag.WardId" required>
                                <option value="">-- اختر العنبر --</option>
                            </select>
                            <span asp-validation-for="WardId" class="filament-form-error"></span>
                            <p class="filament-form-hint">العنبر الذي ستنتمي إليه الغرفة</p>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="filament-form-field">
                            <label asp-for="MaxCapacity" class="filament-form-label required">السعة القصوى *</label>
                            <input asp-for="MaxCapacity" class="filament-form-input" type="number" min="1" max="50" placeholder="عدد النزلاء" required />
                            <span asp-validation-for="MaxCapacity" class="filament-form-error"></span>
                            <p class="filament-form-hint">الحد الأقصى لعدد النزلاء في الغرفة</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="filament-form-field">
                            <div class="filament-form-checkbox">
                                <input asp-for="IsActive" class="filament-form-checkbox-input" type="checkbox" checked />
                                <label asp-for="IsActive" class="filament-form-checkbox-label">
                                    <span class="filament-form-checkbox-indicator"></span>
                                    غرفة نشطة
                                </label>
                            </div>
                            <span asp-validation-for="IsActive" class="filament-form-error"></span>
                            <p class="filament-form-hint">هل الغرفة متاحة للاستخدام</p>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filament-form-field">
                            <label asp-for="Description" class="filament-form-label">الوصف</label>
                            <textarea asp-for="Description" class="filament-form-textarea" rows="4" placeholder="وصف الغرفة ومميزاتها (اختياري)"></textarea>
                            <span asp-validation-for="Description" class="filament-form-error"></span>
                            <p class="filament-form-hint">وصف مختصر للغرفة ومميزاتها</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="filament-form-actions">
            <button type="submit" class="filament-button filament-button-primary filament-button-enhanced">
                <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                حفظ الغرفة
            </button>
            <a href="@Url.Action("Index")" class="filament-button filament-button-secondary filament-button-enhanced">
                <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                إلغاء
            </a>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات تفاعلية للحقول
            const formFields = document.querySelectorAll('.filament-form-input, .filament-form-select, .filament-form-textarea');
            formFields.forEach(field => {
                field.addEventListener('focus', function() {
                    this.closest('.filament-form-field')?.classList.add('focused');
                });

                field.addEventListener('blur', function() {
                    this.closest('.filament-form-field')?.classList.remove('focused');
                });
            });

            // معالجة إرسال النموذج
            const form = document.querySelector('.filament-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitButton = form.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = `
                            <svg class="filament-button-icon animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            جاري الحفظ...
                        `;
                    }
                });
            }
        });
    </script>
}
