using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using PrisonManagementSystem.Data;
using PrisonManagementSystem.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PrisonManagementSystem.Controllers
{
    [Authorize]
    public class PrisonerCasesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PrisonerCasesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: PrisonerCases
        public async Task<IActionResult> Index()
        {
            var cases = await _context.PrisonerCases
                .Include(c => c.Prisoner)
                .OrderByDescending(c => c.CaseDate)
                .ToListAsync();
            return View(cases);
        }

        // GET: PrisonerCases/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prisonerCase = await _context.PrisonerCases
                .Include(c => c.Prisoner)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (prisonerCase == null)
            {
                return NotFound();
            }

            return View(prisonerCase);
        }

        // GET: PrisonerCases/Create
        public IActionResult Create()
        {
            ViewData["PrisonerId"] = new SelectList(_context.Prisoners, "Id", "FullName");
            return View();
        }

        // POST: PrisonerCases/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("PrisonerId,CaseNumber,CaseType,CaseDescription,CaseDate,CourtName,JudgeName,SentenceType,SentenceDuration,SentenceStartDate,SentenceEndDate,IsActive")] PrisonerCase prisonerCase)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    prisonerCase.CreatedDate = DateTime.Now;
                    prisonerCase.CreatedBy = User.Identity.Name;

                    _context.Add(prisonerCase);
                    await _context.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "تم حفظ بيانات القضية بنجاح";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
            }

            ViewData["PrisonerId"] = new SelectList(_context.Prisoners, "Id", "FullName", prisonerCase.PrisonerId);
            return View(prisonerCase);
        }

        // GET: PrisonerCases/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prisonerCase = await _context.PrisonerCases.FindAsync(id);
            if (prisonerCase == null)
            {
                return NotFound();
            }

            ViewData["PrisonerId"] = new SelectList(_context.Prisoners, "Id", "FullName", prisonerCase.PrisonerId);
            return View(prisonerCase);
        }

        // POST: PrisonerCases/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,PrisonerId,CaseNumber,CaseType,CaseDescription,CaseDate,CourtName,JudgeName,SentenceType,SentenceDuration,SentenceStartDate,SentenceEndDate,IsActive,CreatedDate,CreatedBy")] PrisonerCase prisonerCase)
        {
            if (id != prisonerCase.Id)
            {
                return NotFound();
            }

            try
            {
                if (ModelState.IsValid)
                {
                    prisonerCase.UpdatedDate = DateTime.Now;
                    prisonerCase.UpdatedBy = User.Identity.Name;

                    _context.Update(prisonerCase);
                    await _context.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "تم تحديث بيانات القضية بنجاح";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!PrisonerCaseExists(prisonerCase.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "حدث خطأ أثناء تحديث البيانات: " + ex.Message);
            }

            ViewData["PrisonerId"] = new SelectList(_context.Prisoners, "Id", "FullName", prisonerCase.PrisonerId);
            return View(prisonerCase);
        }

        // GET: PrisonerCases/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prisonerCase = await _context.PrisonerCases
                .Include(c => c.Prisoner)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (prisonerCase == null)
            {
                return NotFound();
            }

            return View(prisonerCase);
        }

        // POST: PrisonerCases/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var prisonerCase = await _context.PrisonerCases.FindAsync(id);
                _context.PrisonerCases.Remove(prisonerCase);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = "تم حذف القضية بنجاح";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف القضية: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        private bool PrisonerCaseExists(int id)
        {
            return _context.PrisonerCases.Any(e => e.Id == id);
        }
    }
}
