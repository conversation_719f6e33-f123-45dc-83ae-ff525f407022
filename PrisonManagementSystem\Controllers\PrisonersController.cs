using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using PrisonManagementSystem.Data;
using PrisonManagementSystem.Models;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace PrisonManagementSystem.Controllers
{
    [Authorize]
    public class PrisonersController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PrisonersController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Prisoners
        public async Task<IActionResult> Index()
        {
            var prisoners = await _context.Prisoners
                .Include(p => p.Ward)
                .Include(p => p.Room)
                .Include(p => p.Cases)
                .ToListAsync();
            return View(prisoners);
        }

        // GET: Prisoners/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prisoner = await _context.Prisoners
                .Include(p => p.Ward)
                .Include(p => p.Room)
                .Include(p => p.Cases)
                .Include(p => p.Movements)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (prisoner == null)
            {
                return NotFound();
            }

            return View(prisoner);
        }

        // GET: Prisoners/Create
        public IActionResult Create()
        {
            ViewData["WardId"] = new SelectList(_context.Wards.Where(w => w.IsActive), "Id", "Name");
            ViewData["RoomId"] = new SelectList(_context.Rooms.Where(r => r.IsActive), "Id", "RoomNumber");
            return View();
        }

        // POST: Prisoners/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("PrisonerNumber,FullName,MotherName,DateOfBirth,PlaceOfBirth,Nationality,PassportNumber,IdCardNumber,NationalNumber,PhoneNumber,ClosestRelativeName,RelationshipType,ClosestRelativePhone,ClosestRelativeAddress,PhotoPath,HealthStatus,ChronicDiseases,DisabilityPercentage,EntryDate,HasPreviousImprisonment,WardId,RoomId")] Prisoner prisoner, IFormFile PhotoFile)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // توليد رقم السجين تلقائياً إذا لم يتم إدخاله
                    if (string.IsNullOrEmpty(prisoner.PrisonerNumber))
                    {
                        prisoner.PrisonerNumber = await GeneratePrisonerNumber();
                    }

                    // معالجة رفع الصورة
                    if (PhotoFile != null && PhotoFile.Length > 0)
                    {
                        var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images", "prisoners");
                        if (!Directory.Exists(uploadsFolder))
                        {
                            Directory.CreateDirectory(uploadsFolder);
                        }

                        var uniqueFileName = Guid.NewGuid().ToString() + "_" + PhotoFile.FileName;
                        var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                        using (var fileStream = new FileStream(filePath, FileMode.Create))
                        {
                            await PhotoFile.CopyToAsync(fileStream);
                        }

                        prisoner.PhotoPath = "/images/prisoners/" + uniqueFileName;
                    }

                    // تعيين تاريخ اليوم إذا لم يتم تحديد تاريخ الدخول
                    if (prisoner.EntryDate == default(DateTime))
                    {
                        prisoner.EntryDate = DateTime.Now.Date;
                    }

                    prisoner.CreatedDate = DateTime.Now;
                    prisoner.CreatedBy = User.Identity.Name;

                    _context.Add(prisoner);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "تم حفظ بيانات السجين بنجاح";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ البيانات: " + ex.Message);
            }

            ViewData["WardId"] = new SelectList(_context.Wards.Where(w => w.IsActive), "Id", "Name", prisoner.WardId);
            ViewData["RoomId"] = new SelectList(_context.Rooms.Where(r => r.IsActive), "Id", "RoomNumber", prisoner.RoomId);
            return View(prisoner);
        }

        // GET: Prisoners/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prisoner = await _context.Prisoners.FindAsync(id);
            if (prisoner == null)
            {
                return NotFound();
            }

            ViewData["WardId"] = new SelectList(_context.Wards.Where(w => w.IsActive), "Id", "Name", prisoner.WardId);
            ViewData["RoomId"] = new SelectList(_context.Rooms.Where(r => r.IsActive), "Id", "RoomNumber", prisoner.RoomId);
            return View(prisoner);
        }

        // POST: Prisoners/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,PrisonerNumber,FullName,MotherName,DateOfBirth,PlaceOfBirth,Nationality,PassportNumber,IdCardNumber,NationalNumber,HealthStatus,ChronicDiseases,DisabilityPercentage,EntryDate,HasPreviousImprisonment,ExpectedReleaseDate,TotalSentenceDays,WardId,RoomId,CreatedDate,CreatedBy")] Prisoner prisoner)
        {
            if (id != prisoner.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    prisoner.UpdatedDate = DateTime.Now;
                    prisoner.UpdatedBy = User.Identity.Name;

                    _context.Update(prisoner);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PrisonerExists(prisoner.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["WardId"] = new SelectList(_context.Wards.Where(w => w.IsActive), "Id", "Name", prisoner.WardId);
            ViewData["RoomId"] = new SelectList(_context.Rooms.Where(r => r.IsActive), "Id", "RoomNumber", prisoner.RoomId);
            return View(prisoner);
        }

        // GET: Prisoners/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var prisoner = await _context.Prisoners
                .Include(p => p.Ward)
                .Include(p => p.Room)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (prisoner == null)
            {
                return NotFound();
            }

            return View(prisoner);
        }

        // POST: Prisoners/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var prisoner = await _context.Prisoners.FindAsync(id);
            _context.Prisoners.Remove(prisoner);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Prisoners/Control - صفحة كنترول النزلاء
        public async Task<IActionResult> Control()
        {
            var prisoners = await _context.Prisoners
                .Include(p => p.Ward)
                .Include(p => p.Room)
                .Include(p => p.Cases)
                .Include(p => p.Movements)
                .ToListAsync();
            return View(prisoners);
        }

        // POST: Prisoners/InternalTransfer - نقل داخلي
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> InternalTransfer(int prisonerId, int newWardId, int newRoomId, string reason)
        {
            try
            {
                var prisoner = await _context.Prisoners
                    .Include(p => p.Ward)
                    .Include(p => p.Room)
                    .FirstOrDefaultAsync(p => p.Id == prisonerId);

                if (prisoner == null)
                {
                    return Json(new { success = false, message = "النزيل غير موجود" });
                }

                var newRoom = await _context.Rooms
                    .Include(r => r.Ward)
                    .FirstOrDefaultAsync(r => r.Id == newRoomId);

                if (newRoom == null || newRoom.WardId != newWardId)
                {
                    return Json(new { success = false, message = "الغرفة غير صحيحة" });
                }

                // التحقق من السعة المتاحة
                if (newRoom.CurrentCount >= newRoom.MaxCapacity)
                {
                    return Json(new { success = false, message = "الغرفة ممتلئة" });
                }

                // إنشاء حركة النقل الداخلي
                var movement = new PrisonerMovement
                {
                    PrisonerId = prisonerId,
                    MovementType = "داخلي",
                    MovementCategory = "نقل داخلي",
                    MovementDate = DateTime.Now,
                    FromWardId = prisoner.WardId,
                    FromRoomId = prisoner.RoomId,
                    ToWardId = newWardId,
                    ToRoomId = newRoomId,
                    Reason = reason,
                    Status = "مكتملة",
                    CreatedBy = User.Identity.Name,
                    CreatedDate = DateTime.Now
                };

                // تحديث بيانات النزيل
                if (prisoner.RoomId.HasValue)
                {
                    var oldRoom = await _context.Rooms.FindAsync(prisoner.RoomId.Value);
                    if (oldRoom != null)
                    {
                        oldRoom.CurrentCount = Math.Max(0, oldRoom.CurrentCount - 1);
                    }
                }

                prisoner.WardId = newWardId;
                prisoner.RoomId = newRoomId;
                prisoner.UpdatedDate = DateTime.Now;
                prisoner.UpdatedBy = User.Identity.Name;

                newRoom.CurrentCount++;

                _context.PrisonerMovements.Add(movement);
                _context.Update(prisoner);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "تم النقل الداخلي بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ: " + ex.Message });
            }
        }

        // POST: Prisoners/ExternalTransfer - نقل خارجي
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExternalTransfer(int prisonerId, string movementType, string destination, DateTime? expectedReturnDate, string reason, string notes)
        {
            try
            {
                var prisoner = await _context.Prisoners
                    .Include(p => p.Ward)
                    .Include(p => p.Room)
                    .FirstOrDefaultAsync(p => p.Id == prisonerId);

                if (prisoner == null)
                {
                    return Json(new { success = false, message = "النزيل غير موجود" });
                }

                // إنشاء حركة النقل الخارجي
                var movement = new PrisonerMovement
                {
                    PrisonerId = prisonerId,
                    MovementType = "خارجي",
                    MovementCategory = movementType,
                    MovementDate = DateTime.Now,
                    ExpectedReturnDate = expectedReturnDate,
                    Destination = destination,
                    Reason = reason,
                    Notes = notes,
                    Status = "نشطة",
                    FromWardId = prisoner.WardId,
                    FromRoomId = prisoner.RoomId,
                    CreatedBy = User.Identity.Name,
                    CreatedDate = DateTime.Now
                };

                // إذا كان إفراج، تحديث الغرفة
                if (movementType == "إفراج")
                {
                    if (prisoner.RoomId.HasValue)
                    {
                        var room = await _context.Rooms.FindAsync(prisoner.RoomId.Value);
                        if (room != null)
                        {
                            room.CurrentCount = Math.Max(0, room.CurrentCount - 1);
                        }
                    }
                    prisoner.WardId = null;
                    prisoner.RoomId = null;
                }

                prisoner.UpdatedDate = DateTime.Now;
                prisoner.UpdatedBy = User.Identity.Name;

                _context.PrisonerMovements.Add(movement);
                _context.Update(prisoner);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "تم النقل الخارجي بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ: " + ex.Message });
            }
        }

        // POST: Prisoners/TakeAction - اتخاذ إجراء
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> TakeAction(int prisonerId, string actionDescription, DateTime actionDate)
        {
            try
            {
                var prisoner = await _context.Prisoners.FindAsync(prisonerId);
                if (prisoner == null)
                {
                    return Json(new { success = false, message = "النزيل غير موجود" });
                }

                // إنشاء عقوبة أو إجراء
                var punishment = new PrisonerPunishment
                {
                    PrisonerId = prisonerId,
                    PunishmentType = "إجراء إداري",
                    Description = actionDescription,
                    StartDate = actionDate,
                    EndDate = actionDate,
                    Severity = 1,
                    Status = PunishmentStatus.Active,
                    CreatedBy = User.Identity.Name,
                    CreatedDate = DateTime.Now
                };

                _context.PrisonerPunishments.Add(punishment);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "تم حفظ الإجراء بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ: " + ex.Message });
            }
        }

        // POST: Prisoners/ReturnPrisoner - إعادة النزيل
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnPrisoner(int prisonerId, int wardId, int roomId, string notes)
        {
            try
            {
                var prisoner = await _context.Prisoners
                    .Include(p => p.Movements)
                    .FirstOrDefaultAsync(p => p.Id == prisonerId);

                if (prisoner == null)
                {
                    return Json(new { success = false, message = "النزيل غير موجود" });
                }

                var room = await _context.Rooms
                    .Include(r => r.Ward)
                    .FirstOrDefaultAsync(r => r.Id == roomId);

                if (room == null || room.WardId != wardId)
                {
                    return Json(new { success = false, message = "الغرفة غير صحيحة" });
                }

                // التحقق من السعة المتاحة
                if (room.CurrentCount >= room.MaxCapacity)
                {
                    return Json(new { success = false, message = "الغرفة ممتلئة" });
                }

                // إنهاء الحركة الخارجية النشطة
                var activeMovement = prisoner.Movements
                    .Where(m => m.Status == "نشطة" && m.MovementType == "خارجي")
                    .OrderByDescending(m => m.MovementDate)
                    .FirstOrDefault();

                if (activeMovement != null)
                {
                    activeMovement.ActualReturnDate = DateTime.Now;
                    activeMovement.Status = "مكتملة";
                    activeMovement.Notes += (string.IsNullOrEmpty(activeMovement.Notes) ? "" : " | ") + notes;
                }

                // تحديث بيانات النزيل
                prisoner.WardId = wardId;
                prisoner.RoomId = roomId;
                prisoner.UpdatedDate = DateTime.Now;
                prisoner.UpdatedBy = User.Identity.Name;

                room.CurrentCount++;

                _context.Update(prisoner);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "تم إعادة النزيل بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ: " + ex.Message });
            }
        }

        // GET: Prisoners/GetWards - الحصول على قائمة العنابر
        [HttpGet]
        public async Task<IActionResult> GetWards()
        {
            var wards = await _context.Wards
                .Where(w => w.IsActive)
                .Select(w => new { w.Id, w.Name })
                .ToListAsync();

            return Json(wards);
        }

        // GET: Prisoners/GetRoomsByWard - الحصول على الغرف حسب العنبر
        [HttpGet]
        public async Task<IActionResult> GetRoomsByWard(int wardId)
        {
            var rooms = await _context.Rooms
                .Where(r => r.WardId == wardId && r.IsActive)
                .Select(r => new {
                    r.Id,
                    r.RoomNumber,
                    r.CurrentCount,
                    r.MaxCapacity,
                    Available = r.MaxCapacity - r.CurrentCount
                })
                .ToListAsync();

            return Json(rooms);
        }

        private bool PrisonerExists(int id)
        {
            return _context.Prisoners.Any(e => e.Id == id);
        }

        private async Task<string> GeneratePrisonerNumber()
        {
            var year = DateTime.Now.Year.ToString();
            var lastPrisoner = await _context.Prisoners
                .Where(p => p.PrisonerNumber.StartsWith(year))
                .OrderByDescending(p => p.PrisonerNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastPrisoner != null)
            {
                var lastNumberStr = lastPrisoner.PrisonerNumber.Substring(4);
                if (int.TryParse(lastNumberStr, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{year}{nextNumber:D4}";
        }
    }
}
