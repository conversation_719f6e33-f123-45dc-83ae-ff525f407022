@model PrisonManagementSystem.Models.Prisoner

@{
    ViewData["Title"] = "إضافة نزيل جديد";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="filament-page filament-typography-enhanced">
    <!-- Header Section -->
    <div class="filament-header filament-header-enhanced">
        <div class="filament-header-content">
            <div class="filament-header-heading">
                <h1 class="filament-title-enhanced">
                    <svg class="filament-header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    إضافة نزيل جديد
                </h1>
                <p class="filament-subtitle-enhanced">إدخال بيانات شاملة ودقيقة لنزيل جديد في النظام</p>
            </div>
            <div class="filament-header-actions">
                <a href="@Url.Action("Index")" class="filament-button filament-button-secondary filament-button-enhanced">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <form asp-action="Create" method="post" enctype="multipart/form-data" class="filament-form filament-form-enhanced">
        <div asp-validation-summary="ModelOnly" class="filament-notification filament-notification-danger"></div>

        <!-- Hidden fields for ASP.NET model binding -->
        <input asp-for="FullName" type="hidden" />
        <input asp-for="PrisonerNumber" type="hidden" />

        <!-- Navigation Tabs -->
        <div class="filament-tabs-container">
            <div class="filament-tabs-header">
                <button type="button" class="filament-tab-button active" data-tab="basic">
                    <span class="filament-tab-icon">✓</span>
                    البيانات الأساسية
                </button>
                <button type="button" class="filament-tab-button" data-tab="contact">
                    <span class="filament-tab-icon">✓</span>
                    معلومات الاتصال
                </button>
                <button type="button" class="filament-tab-button" data-tab="prison">
                    <span class="filament-tab-icon">✓</span>
                    بيانات السجن
                </button>
            </div>
        </div>
        <!-- Tab Content: البيانات الأساسية -->
        <div class="filament-tab-content active" id="basic-tab">
            <div class="filament-section">
                <div class="filament-section-header">
                    <div class="filament-section-header-content">
                        <h2 class="filament-section-title">
                            <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            البيانات الأساسية
                        </h2>
                        <p class="filament-section-description">المعلومات الأساسية والشخصية للنزيل</p>
                    </div>
                </div>

                <div class="filament-section-content">
                    <!-- الصف الأول: الأسماء -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">الاسم الأول *</label>
                                <input name="FirstName" class="filament-form-input" placeholder="أدخل الاسم الأول" required />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">اسم الأب *</label>
                                <input name="FatherName" class="filament-form-input" placeholder="أدخل اسم الأب" required />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">اسم الجد *</label>
                                <input name="GrandFatherName" class="filament-form-input" placeholder="أدخل اسم الجد" required />
                            </div>
                        </div>
                    </div>

                    <!-- الصف الثاني: معلومات إضافية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">اسم العائلة *</label>
                                <input name="FamilyName" class="filament-form-input" placeholder="أدخل اسم العائلة" required />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label asp-for="NationalNumber" class="filament-form-label required">رقم الهوية *</label>
                                <input asp-for="NationalNumber" class="filament-form-input" placeholder="أدخل رقم الهوية" required />
                                <span asp-validation-for="NationalNumber" class="filament-form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label asp-for="MotherName" class="filament-form-label required">اسم الأم *</label>
                                <input asp-for="MotherName" class="filament-form-input" placeholder="أدخل اسم الأم" required />
                                <span asp-validation-for="MotherName" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الثالث: معلومات شخصية -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">الجنس *</label>
                                <select name="Gender" class="filament-form-select" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="أنثى">أنثى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">عدد الزوجات *</label>
                                <select name="WivesCount" class="filament-form-select" required>
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">الديانة *</label>
                                <select name="Religion" class="filament-form-select" required>
                                    <option value="">اختر الديانة</option>
                                    <option value="مسلم">مسلم</option>
                                    <option value="مسيحي">مسيحي</option>
                                    <option value="يهودي">يهودي</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="filament-form-field">
                                <label asp-for="DateOfBirth" class="filament-form-label required">تاريخ الميلاد *</label>
                                <input asp-for="DateOfBirth" class="filament-form-input" type="date" required />
                                <span asp-validation-for="DateOfBirth" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الرابع: معلومات إضافية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">الحالة الاجتماعية *</label>
                                <select name="MaritalStatus" class="filament-form-select" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="أعزب">أعزب</option>
                                    <option value="متزوج">متزوج</option>
                                    <option value="مطلق">مطلق</option>
                                    <option value="أرمل">أرمل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">تاريخ الحالة *</label>
                                <input name="MaritalStatusDate" class="filament-form-input" type="date" required />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label required">الحالة الفرعية *</label>
                                <select name="SubStatus" class="filament-form-select" required>
                                    <option value="">اختر الحالة الفرعية</option>
                                    <option value="عادي">عادي</option>
                                    <option value="خاص">خاص</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الخامس: معلومات الهوية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="PlaceOfBirth" class="filament-form-label required">مكان الميلاد *</label>
                                <input asp-for="PlaceOfBirth" class="filament-form-input" placeholder="أدخل مكان الميلاد" required />
                                <span asp-validation-for="PlaceOfBirth" class="filament-form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="Nationality" class="filament-form-label required">الجنسية *</label>
                                <input asp-for="Nationality" class="filament-form-input" placeholder="أدخل الجنسية" required />
                                <span asp-validation-for="Nationality" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>

                    <!-- الصف السادس: أرقام الوثائق -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="PassportNumber" class="filament-form-label">رقم جواز السفر</label>
                                <input asp-for="PassportNumber" class="filament-form-input" placeholder="أدخل رقم جواز السفر" />
                                <span asp-validation-for="PassportNumber" class="filament-form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="IdCardNumber" class="filament-form-label">رقم البطاقة</label>
                                <input asp-for="IdCardNumber" class="filament-form-input" placeholder="أدخل رقم البطاقة" />
                                <span asp-validation-for="IdCardNumber" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>

                    <!-- الصف السابع: الصورة الشخصية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="filament-form-field">
                                <label class="filament-form-label">الصورة الشخصية</label>
                                <div class="filament-photo-upload">
                                    <input type="file" name="PhotoFile" class="filament-form-input" accept="image/*" id="photoInput" />
                                    <div class="filament-photo-preview" id="photoPreview" style="display: none;">
                                        <img id="previewImage" src="" alt="معاينة الصورة" />
                                        <button type="button" class="filament-photo-remove" onclick="removePhoto()">×</button>
                                    </div>
                                    <p class="filament-form-hint">اختر صورة شخصية للنزيل (اختياري) - الحد الأقصى 5MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Content: معلومات الاتصال -->
        <div class="filament-tab-content" id="contact-tab">
            <div class="filament-section">
                <div class="filament-section-header">
                    <div class="filament-section-header-content">
                        <h2 class="filament-section-title">
                            <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            معلومات الاتصال
                        </h2>
                        <p class="filament-section-description">بيانات الاتصال الشخصية ومعلومات أقرب الأقارب</p>
                    </div>
                </div>

                <div class="filament-section-content">
                    <!-- معلومات الاتصال الشخصية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h4 class="filament-subsection-title">معلومات الاتصال الشخصية</h4>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="PhoneNumber" class="filament-form-label">رقم الهاتف</label>
                                <input asp-for="PhoneNumber" class="filament-form-input" placeholder="أدخل رقم الهاتف" />
                                <span asp-validation-for="PhoneNumber" class="filament-form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label class="filament-form-label">العنوان الشخصي</label>
                                <input name="PersonalAddress" class="filament-form-input" placeholder="أدخل العنوان الشخصي" />
                            </div>
                        </div>
                    </div>

                    <!-- معلومات أقرب الأقارب -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h4 class="filament-subsection-title">معلومات أقرب الأقارب</h4>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label asp-for="ClosestRelativeName" class="filament-form-label">اسم أقرب الأقارب</label>
                                <input asp-for="ClosestRelativeName" class="filament-form-input" placeholder="أدخل اسم أقرب الأقارب" />
                                <span asp-validation-for="ClosestRelativeName" class="filament-form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label asp-for="RelationshipType" class="filament-form-label">القرابة</label>
                                <select asp-for="RelationshipType" class="filament-form-select">
                                    <option value="">اختر نوع القرابة</option>
                                    <option value="والد">والد</option>
                                    <option value="والدة">والدة</option>
                                    <option value="أخ">أخ</option>
                                    <option value="أخت">أخت</option>
                                    <option value="زوج">زوج</option>
                                    <option value="زوجة">زوجة</option>
                                    <option value="ابن">ابن</option>
                                    <option value="ابنة">ابنة</option>
                                    <option value="عم">عم</option>
                                    <option value="عمة">عمة</option>
                                    <option value="خال">خال</option>
                                    <option value="خالة">خالة</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                                <span asp-validation-for="RelationshipType" class="filament-form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label asp-for="ClosestRelativePhone" class="filament-form-label">رقم هاتف أقرب الأقارب</label>
                                <input asp-for="ClosestRelativePhone" class="filament-form-input" placeholder="أدخل رقم الهاتف" />
                                <span asp-validation-for="ClosestRelativePhone" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="filament-form-field">
                                <label asp-for="ClosestRelativeAddress" class="filament-form-label">عنوان أقرب الأقارب</label>
                                <textarea asp-for="ClosestRelativeAddress" class="filament-form-textarea" rows="3" placeholder="أدخل العنوان الكامل"></textarea>
                                <span asp-validation-for="ClosestRelativeAddress" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- البيانات الصحية -->
        <div class="filament-section">
            <div class="filament-section-header">
                <div class="filament-section-header-content">
                    <h2 class="filament-section-title">
                        <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        البيانات الصحية
                    </h2>
                    <p class="filament-section-description">المعلومات الطبية والصحية للنزيل</p>
                </div>
            </div>

            <div class="filament-section-content">
                <div class="filament-form-grid">
                    <div class="filament-form-field filament-form-field-full">
                        <label asp-for="HealthStatus" class="filament-form-label">الحالة الصحية</label>
                        <textarea asp-for="HealthStatus" class="filament-form-textarea" rows="3" placeholder="وصف الحالة الصحية العامة للنزيل"></textarea>
                        <span asp-validation-for="HealthStatus" class="filament-form-error"></span>
                    </div>

                    <div class="filament-form-field filament-form-field-full">
                        <label asp-for="ChronicDiseases" class="filament-form-label">الأمراض المزمنة</label>
                        <textarea asp-for="ChronicDiseases" class="filament-form-textarea" rows="3" placeholder="قائمة بالأمراض المزمنة إن وجدت"></textarea>
                        <span asp-validation-for="ChronicDiseases" class="filament-form-error"></span>
                    </div>

                    <div class="filament-form-field">
                        <label asp-for="DisabilityPercentage" class="filament-form-label">نسبة العجز (%)</label>
                        <input asp-for="DisabilityPercentage" class="filament-form-input" type="number" step="0.01" min="0" max="100" placeholder="0.00" />
                        <span asp-validation-for="DisabilityPercentage" class="filament-form-error"></span>
                        <p class="filament-form-hint">أدخل نسبة العجز من 0 إلى 100</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Content: بيانات السجن -->
        <div class="filament-tab-content" id="prison-tab">
            <div class="filament-section">
                <div class="filament-section-header">
                    <div class="filament-section-header-content">
                        <h2 class="filament-section-title">
                            <svg class="filament-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            بيانات السجن
                        </h2>
                        <p class="filament-section-description">معلومات الإقامة والسجن والتسكين</p>
                    </div>
                </div>

                <div class="filament-section-content">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label class="filament-form-label">رقم السجين</label>
                                <input asp-for="PrisonerNumber" class="filament-form-input" placeholder="سيتم توليده تلقائياً" readonly />
                                <span asp-validation-for="PrisonerNumber" class="filament-form-error"></span>
                                <p class="filament-form-hint">سيتم توليده تلقائياً عند الحفظ</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <label asp-for="EntryDate" class="filament-form-label required">تاريخ الدخول *</label>
                                <input asp-for="EntryDate" class="filament-form-input" type="date" value="@DateTime.Now.ToString("yyyy-MM-dd")" required />
                                <span asp-validation-for="EntryDate" class="filament-form-error"></span>
                                <p class="filament-form-hint">تاريخ دخول النزيل للسجن</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filament-form-field">
                                <div class="filament-form-checkbox">
                                    <input asp-for="HasPreviousImprisonment" class="filament-form-checkbox-input" type="checkbox" />
                                    <label asp-for="HasPreviousImprisonment" class="filament-form-checkbox-label">
                                        <span class="filament-form-checkbox-indicator"></span>
                                        هل سبق له الدخول للسجن
                                    </label>
                                </div>
                                <span asp-validation-for="HasPreviousImprisonment" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="WardId" class="filament-form-label">العنبر</label>
                                <select asp-for="WardId" class="filament-form-select" asp-items="ViewBag.WardId">
                                    <option value="">-- اختر العنبر --</option>
                                </select>
                                <span asp-validation-for="WardId" class="filament-form-error"></span>
                                <p class="filament-form-hint">اختر العنبر المناسب للنزيل</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="RoomId" class="filament-form-label">الغرفة</label>
                                <select asp-for="RoomId" class="filament-form-select" asp-items="ViewBag.RoomId">
                                    <option value="">-- اختر الغرفة --</option>
                                </select>
                                <span asp-validation-for="RoomId" class="filament-form-error"></span>
                                <p class="filament-form-hint">اختر الغرفة المناسبة في العنبر</p>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات صحية إضافية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h4 class="filament-subsection-title">المعلومات الصحية</h4>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="HealthStatus" class="filament-form-label">الحالة الصحية</label>
                                <input asp-for="HealthStatus" class="filament-form-input" placeholder="وصف الحالة الصحية العامة" />
                                <span asp-validation-for="HealthStatus" class="filament-form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filament-form-field">
                                <label asp-for="DisabilityPercentage" class="filament-form-label">نسبة العجز (%)</label>
                                <input asp-for="DisabilityPercentage" class="filament-form-input" type="number" min="0" max="100" step="0.1" placeholder="0.0" />
                                <span asp-validation-for="DisabilityPercentage" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="filament-form-field">
                                <label asp-for="ChronicDiseases" class="filament-form-label">الأمراض المزمنة</label>
                                <textarea asp-for="ChronicDiseases" class="filament-form-textarea" rows="3" placeholder="اذكر الأمراض المزمنة إن وجدت"></textarea>
                                <span asp-validation-for="ChronicDiseases" class="filament-form-error"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation and Form Actions -->
        <div class="filament-form-navigation">
            <div class="filament-nav-buttons">
                <button type="button" class="filament-button filament-button-secondary" id="prevBtn" onclick="changeTab(-1)" style="display: none;">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    السابق
                </button>

                <button type="button" class="filament-button filament-button-primary" id="nextBtn" onclick="changeTab(1)">
                    التالي
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <button type="submit" class="filament-button filament-button-success filament-button-enhanced" id="submitBtn" style="display: none;">
                    <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    حفظ البيانات
                </button>
            </div>

            <a href="@Url.Action("Index")" class="filament-button filament-button-secondary filament-button-enhanced">
                <svg class="filament-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                إلغاء
            </a>
        </div>
    </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        let currentTab = 0;
        const tabs = ['basic', 'contact', 'prison'];

        document.addEventListener('DOMContentLoaded', function() {
            // Tab Navigation
            function showTab(tabIndex) {
                // Hide all tab contents
                document.querySelectorAll('.filament-tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // Remove active class from all tab buttons
                document.querySelectorAll('.filament-tab-button').forEach(button => {
                    button.classList.remove('active');
                });

                // Show current tab content
                document.getElementById(tabs[tabIndex] + '-tab').classList.add('active');
                document.querySelector(`[data-tab="${tabs[tabIndex]}"]`).classList.add('active');

                // Update navigation buttons
                document.getElementById('prevBtn').style.display = tabIndex === 0 ? 'none' : 'inline-flex';
                document.getElementById('nextBtn').style.display = tabIndex === tabs.length - 1 ? 'none' : 'inline-flex';
                document.getElementById('submitBtn').style.display = tabIndex === tabs.length - 1 ? 'inline-flex' : 'none';
            }

            window.changeTab = function(direction) {
                currentTab += direction;
                if (currentTab < 0) currentTab = 0;
                if (currentTab >= tabs.length) currentTab = tabs.length - 1;
                showTab(currentTab);
            }

            // Tab button click handlers
            document.querySelectorAll('.filament-tab-button').forEach((button, index) => {
                button.addEventListener('click', () => {
                    currentTab = index;
                    showTab(currentTab);
                });
            });

            // Photo preview functionality
            const photoInput = document.getElementById('photoInput');
            if (photoInput) {
                photoInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Check file size (5MB limit)
                        if (file.size > 5 * 1024 * 1024) {
                            alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
                            this.value = '';
                            return;
                        }

                        // Check file type
                        if (!file.type.startsWith('image/')) {
                            alert('يرجى اختيار ملف صورة صالح');
                            this.value = '';
                            return;
                        }

                        const reader = new FileReader();
                        reader.onload = function(e) {
                            document.getElementById('previewImage').src = e.target.result;
                            document.getElementById('photoPreview').style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            window.removePhoto = function() {
                document.getElementById('photoInput').value = '';
                document.getElementById('photoPreview').style.display = 'none';
            }

            // Auto-generate full name
            function updateFullName() {
                const firstName = document.querySelector('input[name="FirstName"]')?.value || '';
                const fatherName = document.querySelector('input[name="FatherName"]')?.value || '';
                const grandFatherName = document.querySelector('input[name="GrandFatherName"]')?.value || '';
                const familyName = document.querySelector('input[name="FamilyName"]')?.value || '';

                const fullName = [firstName, fatherName, grandFatherName, familyName]
                    .filter(name => name.trim())
                    .join(' ');

                // Update the ASP.NET model field
                const fullNameField = document.querySelector('input[asp-for="FullName"]');
                if (fullNameField) {
                    fullNameField.value = fullName;
                }
            }

            // Add event listeners for name fields
            ['FirstName', 'FatherName', 'GrandFatherName', 'FamilyName'].forEach(fieldName => {
                const field = document.querySelector(`input[name="${fieldName}"]`);
                if (field) {
                    field.addEventListener('input', updateFullName);
                }
            });

            // تحديث قائمة الغرف عند تغيير العنبر
            const wardSelect = document.querySelector('select[name="WardId"]');
            const roomSelect = document.querySelector('select[name="RoomId"]');

            if (wardSelect && roomSelect) {
                wardSelect.addEventListener('change', function() {
                    const wardId = this.value;
                    roomSelect.innerHTML = '<option value="">-- اختر الغرفة --</option>';

                    if (wardId) {
                        fetch(`/Rooms/GetRoomsByWard/${wardId}`)
                            .then(response => response.json())
                            .then(data => {
                                data.forEach(room => {
                                    const option = document.createElement('option');
                                    option.value = room.id;
                                    option.textContent = room.roomNumber;
                                    roomSelect.appendChild(option);
                                });
                            })
                            .catch(error => console.error('Error:', error));
                    }
                });
            }

            // تأثيرات تفاعلية للحقول
            const formFields = document.querySelectorAll('.filament-form-input, .filament-form-select, .filament-form-textarea');
            formFields.forEach(field => {
                field.addEventListener('focus', function() {
                    this.closest('.filament-form-field')?.classList.add('focused');
                });

                field.addEventListener('blur', function() {
                    this.closest('.filament-form-field')?.classList.remove('focused');
                });
            });

            // Form validation before submission
            document.querySelector('form').addEventListener('submit', function(e) {
                const requiredFields = document.querySelectorAll('input[required], select[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    // Go to first tab with errors
                    for (let i = 0; i < tabs.length; i++) {
                        const tabContent = document.getElementById(tabs[i] + '-tab');
                        if (tabContent.querySelector('.is-invalid')) {
                            currentTab = i;
                            showTab(currentTab);
                            break;
                        }
                    }
                    return;
                }

                // Show loading state
                const submitButton = this.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = `
                        <svg class="filament-button-icon animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        جاري الحفظ...
                    `;
                }
            });

            // Initialize
            showTab(0);
        });
    </script>
}

    <style>
        .filament-tabs-container {
            margin-bottom: 2rem;
        }

        .filament-tabs-header {
            display: flex;
            background: white;
            border-radius: 0.75rem;
            padding: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            gap: 0.5rem;
        }

        .filament-tab-button {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border: none;
            background: transparent;
            border-radius: 0.5rem;
            font-family: 'Cairo', sans-serif !important;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .filament-tab-button.active {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .filament-tab-button:hover:not(.active) {
            background: #f3f4f6;
            color: #374151;
        }

        .filament-tab-icon {
            font-size: 0.875rem;
        }

        .filament-tab-content {
            display: none;
        }

        .filament-tab-content.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        .filament-form-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-top: 1px solid #e5e7eb;
            border-radius: 0 0 1rem 1rem;
            margin-top: 2rem;
        }

        .filament-nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .filament-subsection-title {
            font-family: 'Cairo', sans-serif !important;
            font-size: 1.125rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .filament-photo-upload {
            position: relative;
        }

        .filament-photo-preview {
            margin-top: 1rem;
            position: relative;
            display: inline-block;
        }

        .filament-photo-preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .filament-photo-remove {
            position: absolute;
            top: -0.5rem;
            right: -0.5rem;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #ef4444;
            color: white;
            border: none;
            font-size: 1.25rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .is-invalid {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }

        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @@media (max-width: 768px) {
            .filament-tabs-header {
                flex-direction: column;
                gap: 0.25rem;
            }

            .filament-tab-button {
                padding: 0.75rem 1rem;
            }

            .filament-form-navigation {
                flex-direction: column;
                gap: 1rem;
                padding: 1.5rem;
            }

            .filament-nav-buttons {
                order: 2;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
}
