/* أنماط إجبارية لصفحة تسجيل الدخول */

/* متغيرات الألوان العسكرية */
:root {
    --military-primary: #2d5016;
    --military-secondary: #3d6b1f;
    --military-accent: #4a7c28;
    --military-light: #5a8c32;
    --military-dark: #1a3009;
    --military-camo-1: #3d5a27;
    --military-camo-2: #4a6b2f;
    --military-camo-3: #2d4419;
    --military-gold: #d4af37;
    --military-silver: #c0c0c0;
}

/* خلفية عسكرية إجبارية */
html, body {
    background: linear-gradient(135deg, var(--military-camo-1) 0%, var(--military-camo-2) 25%, var(--military-camo-3) 50%, var(--military-camo-1) 75%, var(--military-camo-2) 100%) !important;
    background-size: 400% 400% !important;
    animation: militaryGradient 15s ease infinite !important;
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif !important;
}

@keyframes militaryGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* حاوي تسجيل الدخول */
.login-container {
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: linear-gradient(135deg, var(--military-camo-1) 0%, var(--military-camo-2) 25%, var(--military-camo-3) 50%, var(--military-camo-1) 75%, var(--military-camo-2) 100%) !important;
    background-size: 400% 400% !important;
    animation: militaryGradient 15s ease infinite !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    position: relative !important;
    overflow: hidden !important;
    padding: 2rem !important;
}

.login-container::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%) !important;
    pointer-events: none !important;
}

/* بطاقة تسجيل الدخول */
.login-card {
    background: rgba(255, 255, 255, 0.95) !important;
    padding: 3rem 2.5rem !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3) !important;
    width: 100% !important;
    max-width: 450px !important;
    text-align: center !important;
    border: 3px solid var(--military-gold) !important;
    backdrop-filter: blur(15px) !important;
    position: relative !important;
    overflow: hidden !important;
    z-index: 10 !important;
}

.login-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 5px !important;
    background: linear-gradient(90deg, var(--military-primary), var(--military-gold), var(--military-accent)) !important;
}

/* رأس صفحة تسجيل الدخول */
.login-header {
    margin-bottom: 2.5rem !important;
    position: relative !important;
}

.military-emblem {
    width: 80px !important;
    height: 80px !important;
    background: linear-gradient(135deg, var(--military-primary), var(--military-accent)) !important;
    border-radius: 50% !important;
    margin: 0 auto 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 4px solid var(--military-gold) !important;
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.3) !important;
}

.military-emblem i {
    font-size: 2.5rem !important;
    color: var(--military-gold) !important;
}

.login-header h2 {
    color: var(--military-primary) !important;
    margin-bottom: 0.5rem !important;
    font-size: 2rem !important;
    font-weight: bold !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
}

.login-header p {
    color: var(--military-secondary) !important;
    margin-bottom: 0 !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

/* حقول النموذج */
.form-group {
    margin-bottom: 1.5rem !important;
    text-align: right !important;
    position: relative !important;
}

.form-label, .control-label {
    display: block !important;
    margin-bottom: 0.8rem !important;
    color: var(--military-primary) !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

.form-control {
    width: 100% !important;
    padding: 1rem 1.2rem !important;
    border: 2px solid #e0e0e0 !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    text-align: right !important;
    direction: rtl !important;
    transition: all 0.3s ease !important;
    background: rgba(255,255,255,0.9) !important;
}

.form-control:focus {
    outline: none !important;
    border-color: var(--military-gold) !important;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2) !important;
    background: white !important;
}

.form-check {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    gap: 0.8rem !important;
    margin-bottom: 2rem !important;
}

.form-check-input {
    width: 20px !important;
    height: 20px !important;
    accent-color: var(--military-gold) !important;
}

.form-check-label {
    color: var(--military-secondary) !important;
    font-weight: 500 !important;
}

/* زر تسجيل الدخول */
.btn-primary, .btn-login {
    width: 100% !important;
    padding: 1rem !important;
    background: linear-gradient(135deg, var(--military-primary) 0%, var(--military-accent) 100%) !important;
    border: 2px solid var(--military-gold) !important;
    border-radius: 10px !important;
    color: white !important;
    font-size: 1.1rem !important;
    font-weight: bold !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-primary::before, .btn-login::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
    transition: left 0.5s !important;
}

.btn-primary:hover, .btn-login:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.4) !important;
    background: linear-gradient(135deg, var(--military-accent) 0%, var(--military-light) 100%) !important;
}

.btn-primary:hover::before, .btn-login:hover::before {
    left: 100% !important;
}

.btn-primary:active, .btn-login:active {
    transform: translateY(-1px) !important;
}

/* رسائل الخطأ */
.text-danger {
    color: #dc3545 !important;
    font-size: 0.9rem !important;
    margin-top: 0.5rem !important;
    display: block !important;
    font-weight: 500 !important;
    background: rgba(220, 53, 69, 0.1) !important;
    padding: 0.5rem !important;
    border-radius: 5px !important;
    border-right: 4px solid #dc3545 !important;
}

/* تذييل عسكري */
.military-footer {
    margin-top: 2rem !important;
    padding-top: 1.5rem !important;
    border-top: 2px solid var(--military-gold) !important;
    color: var(--military-secondary) !important;
    font-size: 0.9rem !important;
}
