using Microsoft.AspNetCore.Identity;
using PrisonManagementSystem.Models;

namespace PrisonManagementSystem.Services
{
    public class PlainTextPasswordHasher : IPasswordHasher<ApplicationUser>
    {
        public string HashPassword(ApplicationUser user, string password)
        {
            // إرجاع كلمة المرور كما هي بدون تشفير
            return password;
        }

        public PasswordVerificationResult VerifyHashedPassword(ApplicationUser user, string hashedPassword, string providedPassword)
        {
            // مقارنة كلمة المرور مباشرة بدون تشفير
            if (hashedPassword == providedPassword)
            {
                return PasswordVerificationResult.Success;
            }
            
            return PasswordVerificationResult.Failed;
        }
    }
}
