#pragma checksum "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "18730a43dd7e933a80d10d7fbd99599ef7c3631f384823f46b94b2dc7257f91a"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_PrisonerCases_Index), @"mvc.1.0.view", @"/Views/PrisonerCases/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem

#nullable disable
    ;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"18730a43dd7e933a80d10d7fbd99599ef7c3631f384823f46b94b2dc7257f91a", @"/Views/PrisonerCases/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6b5cda3430a180463c664dbd4c2b7104438aceea37f75cdb0f39bc7283777da", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_PrisonerCases_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
       IEnumerable<PrisonManagementSystem.Models.PrisonerCase>

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("\n");
#nullable restore
#line 3 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
  
    ViewData["Title"] = "إدارة القضايا";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""filament-page"">
    <!-- Header Section -->
    <div class=""filament-header"">
        <div class=""filament-header-content"">
            <div class=""filament-header-heading"">
                <h1 class=""filament-header-title"">
                    <svg class=""filament-header-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z""></path>
                    </svg>
                    إدارة القضايا
                </h1>
                <p class=""filament-header-subtitle"">إدارة ومتابعة جميع القضايا القانونية للنزلاء</p>
            </div>
            <div class=""filament-header-actions"">
                <a");
            BeginWriteAttribute("href", " href=\"", 1005, "\"", 1033, 1);
            WriteAttributeValue("", 1012, 
#nullable restore
#line 22 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                          Url.Action("Create")

#line default
#line hidden
#nullable disable
            , 1012, 21, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-primary"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 6v6m0 0v6m0-6h6m-6 0H6""></path>
                    </svg>
                    إضافة قضية جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class=""filament-stats-grid"">
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-blue"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z""></path>
                    </svg>
                </div>
                <di");
            WriteLiteral("v class=\"filament-stats-details\">\n                    <div class=\"filament-stats-value\">");
            Write(
#nullable restore
#line 42 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                       Model.Count()

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">إجمالي القضايا</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-green"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 56 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                       Model.Count(c => c.IsActive)

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">قضايا نشطة</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-yellow"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 70 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                       Model.Count(c => c.SentenceEndDate.HasValue && c.SentenceEndDate.Value > DateTime.Now)

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">قضايا جارية</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-red"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 84 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                       Model.Count(c => c.SentenceEndDate.HasValue && c.SentenceEndDate.Value <= DateTime.Now)

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">قضايا منتهية</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class=""filament-card"">
        <div class=""filament-card-header"">
            <div class=""filament-card-header-content"">
                <h3 class=""filament-card-title"">قائمة القضايا</h3>
                <p class=""filament-card-description"">إدارة ومتابعة القضايا القانونية</p>
            </div>
            <div class=""filament-card-header-actions"">
                <div class=""filament-search-input"">
                    <svg class=""filament-search-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z""></path>
                    </svg>
                    <input type=""text"" placeholder=""البحث في القضايا..."" class=""filament-search-field"" id=""searchInput"">
                </div>
       ");
            WriteLiteral("     </div>\n        </div>\n\n");
#nullable restore
#line 108 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
         if (Model.Any())
        {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"            <div class=""filament-table-container"">
                <table class=""filament-table"">
                    <thead class=""filament-table-header"">
                        <tr>
                            <th class=""filament-table-header-cell"">رقم القضية</th>
                            <th class=""filament-table-header-cell"">النزيل</th>
                            <th class=""filament-table-header-cell"">نوع القضية</th>
                            <th class=""filament-table-header-cell"">المحكمة</th>
                            <th class=""filament-table-header-cell"">تاريخ القضية</th>
                            <th class=""filament-table-header-cell"">نوع الحكم</th>
                            <th class=""filament-table-header-cell"">مدة الحكم</th>
                            <th class=""filament-table-header-cell"">الحالة</th>
                            <th class=""filament-table-header-cell"">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class=""filament-ta");
            WriteLiteral("ble-body\">\n");
#nullable restore
#line 126 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                         foreach (var item in Model)
                        {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                            <tr class=""filament-table-row"">
                                <td class=""filament-table-cell"">
                                    <div class=""filament-table-cell-content"">
                                        <span class=""filament-badge filament-badge-primary"">");
            Write(
#nullable restore
#line 131 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                                             item.CaseNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</span>
                                    </div>
                                </td>
                                <td class=""filament-table-cell"">
                                    <div class=""filament-table-cell-content"">
                                        <div class=""filament-table-cell-primary"">");
            Write(
#nullable restore
#line 136 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                                  item.Prisoner?.FullName

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                        <div class=\"filament-table-cell-secondary\">");
            Write(
#nullable restore
#line 137 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                                    item.Prisoner?.PrisonerNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                    </div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 141 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                              item.CaseType

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                                </td>
                                <td class=""filament-table-cell"">
                                    <div class=""filament-table-cell-content"">
                                        <div class=""filament-table-cell-primary"">");
            Write(
#nullable restore
#line 145 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                                  item.CourtName

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                        <div class=\"filament-table-cell-secondary\">");
            Write(
#nullable restore
#line 146 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                                    item.JudgeName

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                    </div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 150 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                              item.CaseDate.ToString("yyyy/MM/dd")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 153 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                              item.SentenceType

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">");
            Write(
#nullable restore
#line 156 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                                              item.SentenceDuration

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                                </td>\n                                <td class=\"filament-table-cell\">\n                                    <div class=\"filament-table-cell-content\">\n");
#nullable restore
#line 160 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                         if (item.IsActive)
                                        {
                                            

#line default
#line hidden
#nullable disable

#nullable restore
#line 162 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                             if (item.SentenceEndDate.HasValue && item.SentenceEndDate.Value <= DateTime.Now)
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <span class=\"filament-badge filament-badge-danger\">منتهية</span>\n");
#nullable restore
#line 165 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                            }
                                            else
                                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                                <span class=\"filament-badge filament-badge-success\">نشطة</span>\n");
#nullable restore
#line 169 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                            }

#line default
#line hidden
#nullable disable

#nullable restore
#line 169 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                             
                                        }
                                        else
                                        {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                            <span class=\"filament-badge filament-badge-warning\">غير نشطة</span>\n");
#nullable restore
#line 174 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                        }

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                                    </div>
                                </td>
                                <td class=""filament-table-cell"">
                                    <div class=""filament-table-actions"">
                                        <a");
            BeginWriteAttribute("href", " href=\"", 10247, "\"", 10298, 1);
            WriteAttributeValue("", 10254, 
#nullable restore
#line 179 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                  Url.Action("Details", new { id = item.Id })

#line default
#line hidden
#nullable disable
            , 10254, 44, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-table-action filament-table-action-view"" title=""عرض التفاصيل"">
                                            <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M15 12a3 3 0 11-6 0 3 3 0 016 0z""></path>
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z""></path>
                                            </svg>
                                        </a>
                                        <a");
            BeginWriteAttribute("href", " href=\"", 11034, "\"", 11082, 1);
            WriteAttributeValue("", 11041, 
#nullable restore
#line 185 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                  Url.Action("Edit", new { id = item.Id })

#line default
#line hidden
#nullable disable
            , 11041, 41, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-table-action filament-table-action-edit"" title=""تعديل"">
                                            <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z""></path>
                                            </svg>
                                        </a>
                                        <a");
            BeginWriteAttribute("href", " href=\"", 11647, "\"", 11697, 1);
            WriteAttributeValue("", 11654, 
#nullable restore
#line 190 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                                                  Url.Action("Delete", new { id = item.Id })

#line default
#line hidden
#nullable disable
            , 11654, 43, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-table-action filament-table-action-delete"" title=""حذف"" onclick=""return confirm('هل أنت متأكد من حذف هذه القضية؟')"">
                                            <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16""></path>
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                            </tr>
");
#nullable restore
#line 198 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                        }

#line default
#line hidden
#nullable disable

            WriteLiteral("                    </tbody>\n                </table>\n            </div>\n");
#nullable restore
#line 202 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
        }
        else
        {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"            <div class=""filament-empty-state"">
                <div class=""filament-empty-state-icon"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z""></path>
                    </svg>
                </div>
                <h3 class=""filament-empty-state-title"">لا توجد قضايا مسجلة</h3>
                <p class=""filament-empty-state-description"">ابدأ بإضافة أول قضية في النظام</p>
                <a");
            BeginWriteAttribute("href", " href=\"", 13182, "\"", 13210, 1);
            WriteAttributeValue("", 13189, 
#nullable restore
#line 213 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
                          Url.Action("Create")

#line default
#line hidden
#nullable disable
            , 13189, 21, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-primary"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 6v6m0 0v6m0-6h6m-6 0H6""></path>
                    </svg>
                    إضافة قضية جديدة
                </a>
            </div>
");
#nullable restore
#line 220 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\PrisonerCases\Index.cshtml"
        }

#line default
#line hidden
#nullable disable

            WriteLiteral("    </div>\n</div>\n\n");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        // البحث في القضايا
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('.filament-table-row');
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // تأثيرات تفاعلية
        document.querySelectorAll('.filament-table-row').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<IEnumerable<PrisonManagementSystem.Models.PrisonerCase>> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
