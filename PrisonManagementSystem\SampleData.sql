-- إدراج بيانات تجريبية لنظام إدارة السجن

-- إدراج العنابر
INSERT INTO Wards (Name, Description, MaxCapacity, CurrentCount, IsActive) VALUES
('العنبر الأول', 'عنبر للجرائم العادية', 50, 0, 1),
('العنبر الثاني', 'عنبر للجرائم الخطيرة', 30, 0, 1),
('العنبر الثالث', 'عنبر الأحداث', 20, 0, 1);

-- إ<PERSON><PERSON><PERSON><PERSON> الغرف
INSERT INTO Rooms (RoomNumber, Description, MaxCapacity, CurrentCount, IsActive, WardId) VALUES
('101', 'غرفة 101 - العنبر الأول', 4, 0, 1, 1),
('102', 'غرفة 102 - العنبر الأول', 4, 0, 1, 1),
('103', 'غرفة 103 - العنبر الأول', 4, 0, 1, 1),
('201', 'غرفة 201 - العنبر الثاني', 2, 0, 1, 2),
('202', 'غرفة 202 - العنبر الثاني', 2, 0, 1, 2),
('301', 'غرفة 301 - العنبر الثالث', 6, 0, 1, 3);

-- إدراج النزلاء
INSERT INTO Prisoners (
    PrisonerNumber, FullName, NationalNumber, DateOfBirth, PlaceOfBirth,
    Nationality, EntryDate, ExpectedReleaseDate, WardId, RoomId
) VALUES
('P001', 'أحمد محمد العلي', '19850515001', '1985-05-15', 'بغداد', 'عراقي', DATEADD(day, -30, GETDATE()), DATEADD(day, 365, GETDATE()), 1, 1),
('P002', 'محمد علي الحسن', '19900822002', '1990-08-22', 'البصرة', 'عراقي', DATEADD(day, -15, GETDATE()), DATEADD(day, 180, GETDATE()), 2, 4),
('P003', 'عبدالله حسن الكريم', '19881210003', '1988-12-10', 'الموصل', 'عراقي', DATEADD(day, -60, GETDATE()), DATEADD(day, 90, GETDATE()), 1, 2),
('P004', 'يوسف أحمد السوري', '19920308004', '1992-03-08', 'دمشق', 'سوري', DATEADD(day, -45, GETDATE()), DATEADD(day, 275, GETDATE()), 3, 6),
('P005', 'خالد عبدالرحمن المصري', '19870725005', '1987-07-25', 'القاهرة', 'مصري', DATEADD(day, -20, GETDATE()), DATEADD(day, 200, GETDATE()), 2, 5);

-- إدراج القضايا
INSERT INTO PrisonerCases (
    CaseNumber, Charge, ChargeDetails, Verdict, VerdictStatus,
    CaseDate, Court, Judge, PrisonerId, CreatedDate, CreatedBy
) VALUES
('C001', 'سرقة', 'سرقة محل تجاري', 'السجن لمدة سنة', 'محكوم', DATEADD(day, -35, GETDATE()), 'محكمة الكرخ', 'القاضي أحمد محمد', 1, GETDATE(), 'System'),
('C002', 'اعتداء', 'اعتداء على شخص', 'السجن لمدة 6 أشهر', 'محكوم', DATEADD(day, -20, GETDATE()), 'محكمة الرصافة', 'القاضي علي حسن', 2, GETDATE(), 'System'),
('C003', 'احتيال', 'احتيال مالي', 'السجن لمدة 3 أشهر', 'محكوم', DATEADD(day, -65, GETDATE()), 'محكمة الكرخ', 'القاضي محمد علي', 3, GETDATE(), 'System'),
('C004', 'مخدرات', 'حيازة مواد مخدرة', 'السجن لمدة 9 أشهر', 'محكوم', DATEADD(day, -50, GETDATE()), 'محكمة الرصافة', 'القاضي حسن محمد', 4, GETDATE(), 'System'),
('C005', 'تزوير', 'تزوير وثائق رسمية', 'السجن لمدة 7 أشهر', 'محكوم', DATEADD(day, -25, GETDATE()), 'محكمة الكرخ', 'القاضي عبدالله أحمد', 5, GETDATE(), 'System');

-- تحديث عدد النزلاء في الغرف
UPDATE Rooms SET CurrentCount = (
    SELECT COUNT(*) FROM Prisoners WHERE RoomId = Rooms.Id
);

-- تحديث عدد النزلاء في العنابر
UPDATE Wards SET CurrentCount = (
    SELECT COUNT(*) FROM Prisoners WHERE WardId = Wards.Id
);

PRINT 'تم إدراج البيانات التجريبية بنجاح';