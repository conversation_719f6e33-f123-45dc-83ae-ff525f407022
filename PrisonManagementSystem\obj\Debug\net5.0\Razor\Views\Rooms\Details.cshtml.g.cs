#pragma checksum "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "8fe2d048b373ff46e2c59e05f48b907a4a28cf3f089d6d58ddc6036a25e82aa0"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Rooms_Details), @"mvc.1.0.view", @"/Views/Rooms/Details.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem

#nullable disable
    ;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"8fe2d048b373ff46e2c59e05f48b907a4a28cf3f089d6d58ddc6036a25e82aa0", @"/Views/Rooms/Details.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6b5cda3430a180463c664dbd4c2b7104438aceea37f75cdb0f39bc7283777da", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Rooms_Details : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
       PrisonManagementSystem.Models.Room

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("\n");
#nullable restore
#line 3 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
  
    ViewData["Title"] = "تفاصيل الغرفة";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""filament-page filament-typography-enhanced"">
    <!-- Header Section -->
    <div class=""filament-header filament-header-enhanced"">
        <div class=""filament-header-content"">
            <div class=""filament-header-heading"">
                <h1 class=""filament-title-enhanced"">
                    <svg class=""filament-header-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z""></path>
                    </svg>
                    الغرفة: ");
            Write(
#nullable restore
#line 17 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                             Model.RoomNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("\n                </h1>\n                <p class=\"filament-subtitle-enhanced\">تفاصيل شاملة عن الغرفة والشيلات الموجودة بها</p>\n            </div>\n            <div class=\"filament-header-actions\">\n                <a");
            BeginWriteAttribute("href", " href=\"", 1134, "\"", 1183, 1);
            WriteAttributeValue("", 1141, 
#nullable restore
#line 22 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                          Url.Action("Edit", new { id = Model.Id })

#line default
#line hidden
#nullable disable
            , 1141, 42, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-primary filament-button-enhanced"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z""></path>
                    </svg>
                    تعديل الغرفة
                </a>
                <a");
            BeginWriteAttribute("href", " href=\"", 1692, "\"", 1719, 1);
            WriteAttributeValue("", 1699, 
#nullable restore
#line 28 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                          Url.Action("Index")

#line default
#line hidden
#nullable disable
            , 1699, 20, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-button filament-button-secondary filament-button-enhanced"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M10 19l-7-7m0 0l7-7m-7 7h18""></path>
                    </svg>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class=""filament-stats-grid"">
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-blue"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 ");
            WriteLiteral(@"0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 48 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                       Model.CurrentCount

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">النزلاء الحاليين</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-green"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 62 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                       Model.MaxCapacity

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">السعة القصوى</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-purple"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
                    <div class=""filament-stats-value"">");
            Write(
#nullable restore
#line 76 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                        Model.MaxCapacity - Model.CurrentCount

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    <div class=""filament-stats-label"">أماكن متاحة</div>
                </div>
            </div>
        </div>
        
        <div class=""filament-stats-card"">
            <div class=""filament-stats-content"">
                <div class=""filament-stats-icon filament-stats-icon-yellow"">
                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M13 10V3L4 14h7v7l9-11h-7z""></path>
                    </svg>
                </div>
                <div class=""filament-stats-details"">
");
#nullable restore
#line 90 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                      
                        var occupancyPercentage = Model.MaxCapacity > 0 ? (Model.CurrentCount * 100.0 / Model.MaxCapacity) : 0;
                    

#line default
#line hidden
#nullable disable

            WriteLiteral("                    <div class=\"filament-stats-value\">");
            Write(
#nullable restore
#line 93 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                       occupancyPercentage.ToString("F1")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"%</div>
                    <div class=""filament-stats-label"">نسبة الإشغال</div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الغرفة -->
    <div class=""filament-section"">
        <div class=""filament-section-header"">
            <div class=""filament-section-header-content"">
                <h2 class=""filament-section-title"">
                    <svg class=""filament-section-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z""></path>
                    </svg>
                    معلومات الغرفة
                </h2>
                <p class=""filament-section-description"">البيانات الأساسية والتفصيلية للغرفة</p>
            </div>
        </div>
        
        <div class=""filament-section-content"">
            <div class=""row mb-4"">
                <div class=""col-md-6"">
                    <div class=""filam");
            WriteLiteral("ent-detail-item\">\n                        <label class=\"filament-detail-label\">رقم الغرفة</label>\n                        <div class=\"filament-detail-value\">");
            Write(
#nullable restore
#line 119 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                            Model.RoomNumber

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    </div>
                </div>
                <div class=""col-md-6"">
                    <div class=""filament-detail-item"">
                        <label class=""filament-detail-label"">العنبر</label>
                        <div class=""filament-detail-value"">");
            Write(
#nullable restore
#line 125 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                             Model.Ward?.Name ?? "غير محدد"

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</div>
                    </div>
                </div>
            </div>
            
            <div class=""row mb-4"">
                <div class=""col-md-4"">
                    <div class=""filament-detail-item"">
                        <label class=""filament-detail-label"">السعة القصوى</label>
                        <div class=""filament-detail-value"">");
            Write(
#nullable restore
#line 134 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                            Model.MaxCapacity

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@" نزيل</div>
                    </div>
                </div>
                <div class=""col-md-4"">
                    <div class=""filament-detail-item"">
                        <label class=""filament-detail-label"">العدد الحالي</label>
                        <div class=""filament-detail-value"">");
            Write(
#nullable restore
#line 140 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                            Model.CurrentCount

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@" نزيل</div>
                    </div>
                </div>
                <div class=""col-md-4"">
                    <div class=""filament-detail-item"">
                        <label class=""filament-detail-label"">الحالة</label>
                        <div class=""filament-detail-value"">
");
#nullable restore
#line 147 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                             if (Model.IsActive)
                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                <span class=\"filament-badge filament-badge-success\">نشطة</span>\n");
#nullable restore
#line 150 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                            }
                            else
                            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                <span class=\"filament-badge filament-badge-danger\">غير نشطة</span>\n");
#nullable restore
#line 154 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                            }

#line default
#line hidden
#nullable disable

            WriteLiteral("                        </div>\n                    </div>\n                </div>\n            </div>\n            \n");
#nullable restore
#line 160 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
             if (!string.IsNullOrEmpty(Model.Description))
            {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                <div class=""row mb-4"">
                    <div class=""col-12"">
                        <div class=""filament-detail-item"">
                            <label class=""filament-detail-label"">الوصف</label>
                            <div class=""filament-detail-value"">");
            Write(
#nullable restore
#line 166 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                Model.Description

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</div>\n                        </div>\n                    </div>\n                </div>\n");
#nullable restore
#line 170 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
            }

#line default
#line hidden
#nullable disable

            WriteLiteral(@"            
            <!-- شريط التقدم -->
            <div class=""row mb-4"">
                <div class=""col-12"">
                    <div class=""filament-detail-item"">
                        <label class=""filament-detail-label"">مستوى الإشغال</label>
                        <div class=""filament-room-progress"">
                            <div class=""filament-room-progress-bar"">
");
#nullable restore
#line 179 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                  
                                    var progressClass = occupancyPercentage > 80 ? "filament-progress-danger" : 
                                                      occupancyPercentage > 60 ? "filament-progress-warning" : "filament-progress-success";
                                

#line default
#line hidden
#nullable disable

            WriteLiteral("                                <div");
            BeginWriteAttribute("class", " class=\"", 9763, "\"", 9813, 2);
            WriteAttributeValue("", 9771, "filament-room-progress-fill", 9771, 27, true);
            WriteAttributeValue(" ", 9798, 
#nullable restore
#line 183 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                         progressClass

#line default
#line hidden
#nullable disable
            , 9799, 14, false);
            EndWriteAttribute();
            BeginWriteAttribute("style", " style=\"", 9814, "\"", 9850, 3);
            WriteAttributeValue("", 9822, "width:", 9822, 6, true);
            WriteAttributeValue(" ", 9828, 
#nullable restore
#line 183 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                                                       occupancyPercentage

#line default
#line hidden
#nullable disable
            , 9829, 20, false);
            WriteAttributeValue("", 9849, "%", 9849, 1, true);
            EndWriteAttribute();
            WriteLiteral("></div>\n                            </div>\n                            <span class=\"filament-room-progress-text\">");
            Write(
#nullable restore
#line 185 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                       occupancyPercentage.ToString("F1")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"% ممتلئة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشيلات في الغرفة -->
    <div class=""filament-section"">
        <div class=""filament-section-header"">
            <div class=""filament-section-header-content"">
                <h2 class=""filament-section-title"">
                    <svg class=""filament-section-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z""></path>
                    </svg>
                    الشيلات (غرف العقوبة) في الغرفة
                </h2>
                <p class=""filament-section-description"">قائمة الشيلات (غرف العق");
            WriteLiteral(@"وبة) الموجودة في هذه الغرفة</p>
            </div>
            <div class=""filament-section-actions"">
                <a href=""#"" class=""filament-button filament-button-primary filament-button-sm"">
                    <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 6v6m0 0v6m0-6h6m-6 0H6""></path>
                    </svg>
                    إضافة شيلة جديدة
                </a>
            </div>
        </div>
        
        <div class=""filament-section-content"">
");
#nullable restore
#line 216 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
             if (Model.Shilas != null && Model.Shilas.Any())
            {

#line default
#line hidden
#nullable disable

            WriteLiteral("                <div class=\"filament-grid-container\">\n");
#nullable restore
#line 219 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                     foreach (var shila in Model.Shilas)
                    {

#line default
#line hidden
#nullable disable

            WriteLiteral("                        <div class=\"filament-shila-card\">\n                            <div class=\"filament-shila-card-header\">\n                                <h4 class=\"filament-shila-card-title\">");
            Write(
#nullable restore
#line 223 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                       shila.Name

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</h4>\n");
#nullable restore
#line 224 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                 if (shila.IsActive)
                                {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                    <span class=\"filament-badge filament-badge-success\">نشطة</span>\n");
#nullable restore
#line 227 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                }
                                else
                                {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                    <span class=\"filament-badge filament-badge-danger\">غير نشطة</span>\n");
#nullable restore
#line 231 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                }

#line default
#line hidden
#nullable disable

            WriteLiteral("                            </div>\n                            \n                            <div class=\"filament-shila-card-content\">\n");
#nullable restore
#line 235 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                 if (!string.IsNullOrEmpty(shila.Description))
                                {

#line default
#line hidden
#nullable disable

            WriteLiteral("                                    <p class=\"filament-shila-description\">");
            Write(
#nullable restore
#line 237 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                           shila.Description

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("</p>\n");
#nullable restore
#line 238 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                }

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                                
                                <div class=""filament-shila-stats"">
                                    <div class=""filament-shila-stat"">
                                        <span class=""filament-shila-stat-label"">العدد الحالي</span>
                                        <span class=""filament-shila-stat-value"">");
            Write(
#nullable restore
#line 243 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                                 shila.CurrentCount

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</span>
                                    </div>
                                    <div class=""filament-shila-stat"">
                                        <span class=""filament-shila-stat-label"">السعة القصوى</span>
                                        <span class=""filament-shila-stat-value"">");
            Write(
#nullable restore
#line 247 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                                 shila.MaxCapacity

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"</span>
                                    </div>
                                </div>
                                
                                <div class=""filament-shila-progress"">
                                    <div class=""filament-shila-progress-bar"">
");
#nullable restore
#line 253 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                          
                                            var shilaOccupancy = shila.MaxCapacity > 0 ? (shila.CurrentCount * 100.0 / shila.MaxCapacity) : 0;
                                            var shilaProgressClass = shilaOccupancy > 80 ? "filament-progress-danger" : 
                                                                   shilaOccupancy > 60 ? "filament-progress-warning" : "filament-progress-success";
                                        

#line default
#line hidden
#nullable disable

            WriteLiteral("                                        <div");
            BeginWriteAttribute("class", " class=\"", 14367, "\"", 14423, 2);
            WriteAttributeValue("", 14375, "filament-shila-progress-fill", 14375, 28, true);
            WriteAttributeValue(" ", 14403, 
#nullable restore
#line 258 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                                  shilaProgressClass

#line default
#line hidden
#nullable disable
            , 14404, 19, false);
            EndWriteAttribute();
            BeginWriteAttribute("style", " style=\"", 14424, "\"", 14455, 3);
            WriteAttributeValue("", 14432, "width:", 14432, 6, true);
            WriteAttributeValue(" ", 14438, 
#nullable restore
#line 258 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                                                                     shilaOccupancy

#line default
#line hidden
#nullable disable
            , 14439, 15, false);
            WriteAttributeValue("", 14454, "%", 14454, 1, true);
            EndWriteAttribute();
            WriteLiteral("></div>\n                                    </div>\n                                    <span class=\"filament-shila-progress-text\">");
            Write(
#nullable restore
#line 260 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                                                                                shilaOccupancy.ToString("F1")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral(@"% ممتلئة</span>
                                </div>
                            </div>
                            
                            <div class=""filament-shila-card-actions"">
                                <a href=""#"" class=""filament-shila-action"" title=""عرض التفاصيل"">
                                    <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M15 12a3 3 0 11-6 0 3 3 0 016 0z""></path>
                                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z""></path>
                                    </svg>
                                </a>
                                <a href=""#"" class=""filament-shila-action"" title=""تعديل"">
                                    <svg fill=""none"" stroke=""currentColor"" vi");
            WriteLiteral(@"ewBox=""0 0 24 24"">
                                        <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z""></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
");
#nullable restore
#line 278 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
                    }

#line default
#line hidden
#nullable disable

            WriteLiteral("                </div>\n");
#nullable restore
#line 280 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
            }
            else
            {

#line default
#line hidden
#nullable disable

            WriteLiteral(@"                <div class=""filament-empty-state"">
                    <div class=""filament-empty-state-icon"">
                        <svg fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
                            <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z""></path>
                        </svg>
                    </div>
                    <h3 class=""filament-empty-state-title"">لا توجد شيلات في هذه الغرفة</h3>
                    <p class=""filament-empty-state-description"">ابدأ بإضافة أول شيلة في الغرفة</p>
                    <a href=""#"" class=""filament-button filament-button-primary"">
                        <svg class=""filament-button-icon"" fill=""none"" stroke=""currentColor"" viewBox=""0 0 24 24"">
             ");
            WriteLiteral("               <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                        </svg>\n                        إضافة شيلة جديدة\n                    </a>\n                </div>\n");
#nullable restore
#line 298 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Rooms\Details.cshtml"
            }

#line default
#line hidden
#nullable disable

            WriteLiteral("        </div>\n    </div>\n</div>\n\n");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.filament-shila-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            });
        });
    </script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<PrisonManagementSystem.Models.Room> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
