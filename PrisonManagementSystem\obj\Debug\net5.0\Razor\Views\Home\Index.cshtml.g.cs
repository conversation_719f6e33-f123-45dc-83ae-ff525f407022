#pragma checksum "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "77f93f7f41f48b4af292680431e271d41de198fe4ce45d46d99539419947b4aa"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Home_Index), @"mvc.1.0.view", @"/Views/Home/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem

#nullable disable
    ;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\_ViewImports.cshtml"
using PrisonManagementSystem.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"77f93f7f41f48b4af292680431e271d41de198fe4ce45d46d99539419947b4aa", @"/Views/Home/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6b5cda3430a180463c664dbd4c2b7104438aceea37f75cdb0f39bc7283777da", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Home_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/logo.svg"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString("شعار القوات المسلحة"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("width: 40px; height: 40px; margin-left: 12px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
  
    ViewData["Title"] = "الصفحة الرئيسية";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<!-- Stats Cards -->
<div class=""filament-stats"">
    <div class=""filament-stat-card"">
        <div class=""filament-stat-icon primary"">
            <i class=""fas fa-users""></i>
        </div>
        <div class=""filament-stat-value"">125</div>
        <div class=""filament-stat-label"">إجمالي النزلاء</div>
    </div>

    <div class=""filament-stat-card"">
        <div class=""filament-stat-icon success"">
            <i class=""fas fa-building""></i>
        </div>
        <div class=""filament-stat-value"">3</div>
        <div class=""filament-stat-label"">العنابر النشطة</div>
    </div>

    <div class=""filament-stat-card"">
        <div class=""filament-stat-icon warning"">
            <i class=""fas fa-door-open""></i>
        </div>
        <div class=""filament-stat-value"">18</div>
        <div class=""filament-stat-label"">الغرف المتاحة</div>
    </div>

    <div class=""filament-stat-card"">
        <div class=""filament-stat-icon danger"">
            <i class=""fas fa-gavel""></i>
        </div");
            WriteLiteral(@">
        <div class=""filament-stat-value"">45</div>
        <div class=""filament-stat-label"">القضايا النشطة</div>
    </div>
</div>

<!-- Welcome Card -->
<div class=""filament-card mb-6"">
    <div class=""filament-card-header military-header"">
        <h2 class=""filament-card-title"">
            ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "77f93f7f41f48b4af292680431e271d41de198fe4ce45d46d99539419947b4aa6163", async() => {
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            مرحباً بك في نظام إدارة سجن الكويفية
        </h2>
    </div>
    <div class=""filament-card-content"">
        <div class=""text-center"">
            <p style=""font-size: 18px; color: var(--gray-700); margin-bottom: 24px;"">
                نظام شامل لإدارة السجون والنزلاء - الكتيبة 210 مشاة آلية
            </p>
            <div style=""display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-top: 32px;"">
                <a");
            BeginWriteAttribute("href", " href=\"", 2029, "\"", 2071, 1);
            WriteAttributeValue("", 2036, 
#nullable restore
#line 55 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Control", "Prisoners")

#line default
#line hidden
#nullable disable
            , 2036, 35, false);
            EndWriteAttribute();
            WriteLiteral(" class=\"filament-btn filament-btn-primary filament-btn-lg\">\r\n                    <i class=\"fas fa-list-alt\"></i>\r\n                    كنترول النزلاء\r\n                </a>\r\n                <a");
            BeginWriteAttribute("href", " href=\"", 2262, "\"", 2302, 1);
            WriteAttributeValue("", 2269, 
#nullable restore
#line 59 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Index", "Prisoners")

#line default
#line hidden
#nullable disable
            , 2269, 33, false);
            EndWriteAttribute();
            WriteLiteral(" class=\"filament-btn filament-btn-secondary filament-btn-lg\">\r\n                    <i class=\"fas fa-users\"></i>\r\n                    إدارة النزلاء\r\n                </a>\r\n                <a");
            BeginWriteAttribute("href", " href=\"", 2491, "\"", 2527, 1);
            WriteAttributeValue("", 2498, 
#nullable restore
#line 63 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Index", "Wards")

#line default
#line hidden
#nullable disable
            , 2498, 29, false);
            EndWriteAttribute();
            WriteLiteral(" class=\"filament-btn filament-btn-secondary filament-btn-lg\">\r\n                    <i class=\"fas fa-building\"></i>\r\n                    إدارة العنابر\r\n                </a>\r\n                <a");
            BeginWriteAttribute("href", " href=\"", 2719, "\"", 2763, 1);
            WriteAttributeValue("", 2726, 
#nullable restore
#line 67 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Index", "PrisonerCases")

#line default
#line hidden
#nullable disable
            , 2726, 37, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-btn filament-btn-secondary filament-btn-lg"">
                    <i class=""fas fa-gavel""></i>
                    إدارة القضايا
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Grid -->
<div style=""display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; margin-bottom: 32px;"">
    <!-- إدارة النزلاء -->
    <div class=""filament-card"">
        <div class=""filament-card-header"">
            <h3 class=""filament-card-title"">
                <i class=""fas fa-users""></i>
                إدارة النزلاء
            </h3>
        </div>
        <div class=""filament-card-content"">
            <div style=""display: flex; flex-direction: column; gap: 12px;"">
                <a");
            BeginWriteAttribute("href", " href=\"", 3547, "\"", 3588, 1);
            WriteAttributeValue("", 3554, 
#nullable restore
#line 88 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Create", "Prisoners")

#line default
#line hidden
#nullable disable
            , 3554, 34, false);
            EndWriteAttribute();
            WriteLiteral(" class=\"filament-btn filament-btn-secondary\">\r\n                    <i class=\"fas fa-user-plus\"></i>\r\n                    إضافة نزيل جديد\r\n                </a>\r\n                <a");
            BeginWriteAttribute("href", " href=\"", 3767, "\"", 3807, 1);
            WriteAttributeValue("", 3774, 
#nullable restore
#line 92 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Index", "Prisoners")

#line default
#line hidden
#nullable disable
            , 3774, 33, false);
            EndWriteAttribute();
            WriteLiteral(" class=\"filament-btn filament-btn-secondary\">\r\n                    <i class=\"fas fa-list\"></i>\r\n                    قائمة النزلاء\r\n                </a>\r\n                <a");
            BeginWriteAttribute("href", " href=\"", 3979, "\"", 4027, 1);
            WriteAttributeValue("", 3986, 
#nullable restore
#line 96 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Index", "PrisonerMovements")

#line default
#line hidden
#nullable disable
            , 3986, 41, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-btn filament-btn-secondary"">
                    <i class=""fas fa-exchange-alt""></i>
                    حركات النزلاء
                </a>
            </div>
        </div>
    </div>

    <!-- إدارة المرافق -->
    <div class=""filament-card"">
        <div class=""filament-card-header"">
            <h3 class=""filament-card-title"">
                <i class=""fas fa-building""></i>
                إدارة المرافق
            </h3>
        </div>
        <div class=""filament-card-content"">
            <div style=""display: flex; flex-direction: column; gap: 12px;"">
                <a");
            BeginWriteAttribute("href", " href=\"", 4645, "\"", 4681, 1);
            WriteAttributeValue("", 4652, 
#nullable restore
#line 114 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Index", "Wards")

#line default
#line hidden
#nullable disable
            , 4652, 29, false);
            EndWriteAttribute();
            WriteLiteral(" class=\"filament-btn filament-btn-secondary\">\r\n                    <i class=\"fas fa-building\"></i>\r\n                    إدارة العنابر\r\n                </a>\r\n                <a");
            BeginWriteAttribute("href", " href=\"", 4857, "\"", 4893, 1);
            WriteAttributeValue("", 4864, 
#nullable restore
#line 118 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Index", "Rooms")

#line default
#line hidden
#nullable disable
            , 4864, 29, false);
            EndWriteAttribute();
            WriteLiteral(" class=\"filament-btn filament-btn-secondary\">\r\n                    <i class=\"fas fa-door-open\"></i>\r\n                    إدارة الغرف\r\n                </a>\r\n                <a");
            BeginWriteAttribute("href", " href=\"", 5068, "\"", 5105, 1);
            WriteAttributeValue("", 5075, 
#nullable restore
#line 122 "C:\Users\<USER>\Documents\augment-projects\code_z\PrisonManagementSystem\Views\Home\Index.cshtml"
                          Url.Action("Create", "Wards")

#line default
#line hidden
#nullable disable
            , 5075, 30, false);
            EndWriteAttribute();
            WriteLiteral(@" class=""filament-btn filament-btn-secondary"">
                    <i class=""fas fa-plus-circle""></i>
                    إضافة عنبر جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class=""filament-card"">
    <div class=""filament-card-header"">
        <h3 class=""filament-card-title"">
            <i class=""fas fa-clock""></i>
            الأنشطة الأخيرة
        </h3>
    </div>
    <div class=""filament-card-content"">
        <div style=""display: flex; flex-direction: column; gap: 16px;"">
            <div style=""display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;"">
                <div style=""width: 40px; height: 40px; background: var(--primary-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;"">
                    <i class=""fas fa-user-plus"" style=""color: var(--primary-600);""></i>
                </div>
                <div style=""flex");
            WriteLiteral(@": 1;"">
                    <div style=""font-weight: 600; color: var(--gray-900);"">تم إضافة نزيل جديد</div>
                    <div style=""font-size: 14px; color: var(--gray-600);"">أحمد محمد العلي - العنبر الأول</div>
                </div>
                <div style=""font-size: 12px; color: var(--gray-500);"">منذ ساعتين</div>
            </div>

            <div style=""display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;"">
                <div style=""width: 40px; height: 40px; background: var(--success-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;"">
                    <i class=""fas fa-gavel"" style=""color: var(--success-500);""></i>
                </div>
                <div style=""flex: 1;"">
                    <div style=""font-weight: 600; color: var(--gray-900);"">تم تحديث قضية</div>
                    <div style=""font-size: 14px; color: var(--gray-600);"">قضية رقم C001 - تم تحديد موعد المحاكمة</di");
            WriteLiteral(@"v>
                </div>
                <div style=""font-size: 12px; color: var(--gray-500);"">منذ 4 ساعات</div>
            </div>

            <div style=""display: flex; align-items: center; gap: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;"">
                <div style=""width: 40px; height: 40px; background: var(--warning-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;"">
                    <i class=""fas fa-exchange-alt"" style=""color: var(--warning-500);""></i>
                </div>
                <div style=""flex: 1;"">
                    <div style=""font-weight: 600; color: var(--gray-900);"">حركة نقل</div>
                    <div style=""font-size: 14px; color: var(--gray-600);"">نقل نزيل من العنبر الثاني إلى العنبر الأول</div>
                </div>
                <div style=""font-size: 12px; color: var(--gray-500);"">أمس</div>
            </div>
        </div>
    </div>
</div>


");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
