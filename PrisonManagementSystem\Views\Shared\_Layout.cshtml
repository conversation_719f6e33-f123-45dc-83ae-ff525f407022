﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة سجن الكويفية</title>
    <link rel="icon" type="image/svg+xml" href="~/images/logo-icon.svg">
    <link rel="alternate icon" href="~/favicon.ico">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/military-theme.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />

</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark military-navbar">
            <div class="container">
                <a class="navbar-brand military-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <img src="~/images/logo.svg" alt="شعار القيادة العامة للقوات المسلحة العربية الليبية" style="width: 40px; height: 40px; margin-left: 10px;">
                    <span class="brand-text">نظام إدارة سجن الكويفية</span>
                    <div class="brand-subtitle">الكتيبة 210 مشاة آلية</div>
                </a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1 military-nav">
                        <li class="nav-item">
                            <a class="nav-link military-nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home"></i>
                                <span>الرئيسية</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link military-nav-link" asp-area="" asp-controller="Prisoners" asp-action="Control">
                                <i class="fas fa-list-alt"></i>
                                <span>كنترول النزلاء</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link military-nav-link" asp-area="" asp-controller="Prisoners" asp-action="Index">
                                <i class="fas fa-users"></i>
                                <span>قائمة النزلاء</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link military-nav-link" asp-area="" asp-controller="Wards" asp-action="Index">
                                <i class="fas fa-building"></i>
                                <span>العنابر</span>
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle military-user-menu" href="#" id="navbarDropdown" role="button" data-toggle="dropdown">
                                <div class="user-avatar">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <span class="user-name">@User.Identity.Name</span>
                                <span class="user-rank">مدير النظام</span>
                            </a>
                            <div class="dropdown-menu military-dropdown">
                                <a class="dropdown-item military-dropdown-item" href="#">
                                    <i class="fas fa-user-cog"></i> الملف الشخصي
                                </a>
                                <a class="dropdown-item military-dropdown-item" href="#">
                                    <i class="fas fa-cog"></i> الإعدادات
                                </a>
                                <div class="dropdown-divider"></div>
                                <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item military-dropdown-item logout-btn">
                                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                    </button>
                                </form>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>


    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - نظام إدارة سجن الكويفية - جميع الحقوق محفوظة
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
