using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PrisonManagementSystem.Models
{
    public class PrisonerMovement
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع الحركة")]
        public string MovementType { get; set; } // داخلي، خارجي

        [Required]
        [StringLength(100)]
        [Display(Name = "تصنيف الحركة")]
        public string MovementCategory { get; set; } // إفراج، تشغيل خارجي، مأمورية، نقل داخلي

        [Display(Name = "تاريخ الحركة")]
        public DateTime MovementDate { get; set; }

        [Display(Name = "تاريخ العودة المتوقع")]
        public DateTime? ExpectedReturnDate { get; set; }

        [Display(Name = "تاريخ العودة الفعلي")]
        public DateTime? ActualReturnDate { get; set; }

        [StringLength(200)]
        [Display(Name = "الوجهة")]
        public string Destination { get; set; }

        [StringLength(500)]
        [Display(Name = "السبب")]
        public string Reason { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string Notes { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "حالة الحركة")]
        public string Status { get; set; } // نشطة، مكتملة، ملغية

        // بيانات النقل الداخلي
        [Display(Name = "العنبر السابق")]
        public int? FromWardId { get; set; }
        [ForeignKey("FromWardId")]
        public virtual Ward FromWard { get; set; }

        [Display(Name = "الغرفة السابقة")]
        public int? FromRoomId { get; set; }
        [ForeignKey("FromRoomId")]
        public virtual Room FromRoom { get; set; }

        [Display(Name = "العنبر الجديد")]
        public int? ToWardId { get; set; }
        [ForeignKey("ToWardId")]
        public virtual Ward ToWard { get; set; }

        [Display(Name = "الغرفة الجديدة")]
        public int? ToRoomId { get; set; }
        [ForeignKey("ToRoomId")]
        public virtual Room ToRoom { get; set; }

        // العلاقة مع السجين
        [Required]
        [Display(Name = "السجين")]
        public int PrisonerId { get; set; }
        [ForeignKey("PrisonerId")]
        public virtual Prisoner Prisoner { get; set; }

        // تواريخ النظام
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        [Display(Name = "المستخدم المنشئ")]
        public string CreatedBy { get; set; }

        [StringLength(50)]
        [Display(Name = "المستخدم المعتمد")]
        public string ApprovedBy { get; set; }

        [Display(Name = "تاريخ الاعتماد")]
        public DateTime? ApprovedDate { get; set; }
    }
}
