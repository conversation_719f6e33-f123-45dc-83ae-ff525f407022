using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PrisonManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PrisonManagementSystem.Data
{
    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            var context = serviceProvider.GetRequiredService<ApplicationDbContext>();

            try
            {
                // إنشاء الأدوار
                await CreateRoles(roleManager, logger);

                // إنشاء المستخدم الافتراضي
                await CreateDefaultUser(userManager, logger);

                // إنشاء العنابر والغرف الافتراضية
                await CreateWardsAndRooms(context, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding the database.");
            }
        }

        private static async Task CreateRoles(RoleManager<IdentityRole> roleManager, ILogger logger)
        {
            string[] roleNames = { "Admin", "Officer", "Guard", "Viewer" };

            foreach (var roleName in roleNames)
            {
                var roleExist = await roleManager.RoleExistsAsync(roleName);
                if (!roleExist)
                {
                    var result = await roleManager.CreateAsync(new IdentityRole(roleName));
                    if (result.Succeeded)
                    {
                        logger.LogInformation($"Role {roleName} created successfully.");
                    }
                    else
                    {
                        logger.LogError($"Error creating role {roleName}: {string.Join(", ", result.Errors)}");
                    }
                }
            }
        }

        private static async Task CreateDefaultUser(UserManager<ApplicationUser> userManager, ILogger logger)
        {
            var defaultUser = await userManager.FindByNameAsync("admin");
            
            if (defaultUser == null)
            {
                var user = new ApplicationUser
                {
                    UserName = "admin",
                    Email = "<EMAIL>",
                    EmailConfirmed = true,
                    FullName = "مدير النظام",
                    PhoneNumber = "+964-XXX-XXXX",
                    EmployeeNumber = "001",
                    Position = "مدير النظام",
                    Department = "تقنية المعلومات",
                    HireDate = DateTime.Now,
                    IsActive = true
                };

                var result = await userManager.CreateAsync(user, "123");
                
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(user, "Admin");
                    logger.LogInformation("Default admin user created successfully.");
                }
                else
                {
                    logger.LogError($"Error creating default user: {string.Join(", ", result.Errors)}");
                }
            }
            else
            {
                logger.LogInformation("Default admin user already exists.");
            }
        }

        private static async Task CreateWardsAndRooms(ApplicationDbContext context, ILogger logger)
        {
            // إنشاء العنابر إذا لم تكن موجودة
            if (!context.Wards.Any())
            {
                var wards = new[]
                {
                    new Ward { Name = "العنبر الأول", Description = "عنبر للسجناء العاديين", MaxCapacity = 50, IsActive = true },
                    new Ward { Name = "العنبر الثاني", Description = "عنبر للسجناء المحكومين", MaxCapacity = 40, IsActive = true },
                    new Ward { Name = "العنبر الثالث", Description = "عنبر للسجناء الموقوفين", MaxCapacity = 30, IsActive = true },
                    new Ward { Name = "العنبر الطبي", Description = "عنبر للحالات الطبية", MaxCapacity = 20, IsActive = true }
                };

                context.Wards.AddRange(wards);
                await context.SaveChangesAsync();
                logger.LogInformation("Default wards created successfully.");
            }

            // إنشاء الغرف إذا لم تكن موجودة
            if (!context.Rooms.Any())
            {
                var rooms = new List<Room>();
                var wards = context.Wards.ToList();

                foreach (var ward in wards)
                {
                    for (int i = 1; i <= 10; i++)
                    {
                        rooms.Add(new Room
                        {
                            RoomNumber = $"{ward.Name.Replace("العنبر ", "")}-{i:D2}",
                            WardId = ward.Id,
                            MaxCapacity = 5,
                            IsActive = true
                        });
                    }
                }

                context.Rooms.AddRange(rooms);
                await context.SaveChangesAsync();
                logger.LogInformation("Default rooms created successfully.");
            }
        }
    }
}
