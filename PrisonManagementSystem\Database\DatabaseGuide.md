# 📊 دليل قاعدة بيانات نظام إدارة سجن الكويفية

## 🔍 **معلومات قاعدة البيانات:**

### **التفاصيل الأساسية:**
- **اسم قاعدة البيانات:** `PrisonManagementDB`
- **نوع قاعدة البيانات:** SQL Server LocalDB
- **سلسلة الاتصال:** `Server=(localdb)\\mssqllocaldb;Database=PrisonManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true`

### **الموقع الفعلي:**
```
C:\Users\<USER>\AppData\Local\Microsoft\Microsoft SQL Server Local DB\Instances\MSSQLLocalDB\
```

## 🗂️ **هيكل قاعدة البيانات:**

### **1. جداول النظام الأساسية:**

#### **Wards (العنابر)**
- `Id` - المعرف الفريد
- `Name` - اسم العنبر
- `Description` - وصف العنبر
- `MaxCapacity` - السعة القصوى
- `CurrentCount` - العدد الحالي
- `IsActive` - حالة النشاط

#### **Rooms (الغرف)**
- `Id` - المعرف الفريد
- `RoomNumber` - رقم الغرفة
- `Description` - وصف الغرفة
- `MaxCapacity` - السعة القصوى
- `CurrentCount` - العدد الحالي
- `IsActive` - حالة النشاط
- `WardId` - معرف العنبر (مفتاح خارجي)

#### **Shilas (الشيل)**
- `Id` - المعرف الفريد
- `Name` - اسم الشيلة
- `Description` - وصف الشيلة
- `MaxCapacity` - السعة القصوى
- `CurrentCount` - العدد الحالي
- `IsActive` - حالة النشاط
- `RoomId` - معرف الغرفة (مفتاح خارجي)

### **2. جداول النزلاء:**

#### **Prisoners (النزلاء)**
**البيانات الشخصية:**
- `Id` - المعرف الفريد
- `PrisonerNumber` - رقم السجين (فريد)
- `FullName` - الاسم الرباعي
- `MotherName` - اسم الأم
- `DateOfBirth` - تاريخ الميلاد
- `PlaceOfBirth` - مكان الميلاد
- `Nationality` - الجنسية
- `PassportNumber` - رقم جواز السفر
- `IdCardNumber` - رقم البطاقة
- `NationalNumber` - الرقم الوطني (فريد)

**البيانات الصحية:**
- `HealthStatus` - الحالة الصحية
- `ChronicDiseases` - الأمراض المزمنة
- `DisabilityPercentage` - نسبة العجز

**بيانات السجن:**
- `EntryDate` - تاريخ الدخول
- `HasPreviousImprisonment` - هل سبق له الدخول
- `ExpectedReleaseDate` - تاريخ الإفراج المتوقع
- `TotalSentenceDays` - إجمالي أيام الحكم
- `WardId` - معرف العنبر
- `RoomId` - معرف الغرفة

#### **PrisonerCases (قضايا النزلاء)**
- `Id` - المعرف الفريد
- `CaseNumber` - رقم القضية
- `Charge` - التهمة
- `ChargeDetails` - تفاصيل التهمة
- `Verdict` - الحكم
- `SentenceDays` - أيام الحكم
- `Fine` - الغرامة
- `SentenceStartDate` - تاريخ بداية الحكم
- `SentenceEndDate` - تاريخ انتهاء الحكم
- `VerdictStatus` - حالة الحكم (محكوم/موقوف/مبرأ)
- `CaseDate` - تاريخ القضية
- `Court` - المحكمة
- `Judge` - القاضي
- `PrisonerId` - معرف السجين

### **3. جداول الحركات:**

#### **MovementTypes (أنواع الحركات)**
- `Id` - المعرف الفريد
- `Name` - اسم نوع الحركة
- `Description` - الوصف
- `Category` - التصنيف (داخلي/خارجي)
- `RequiresReturnDate` - يتطلب تاريخ عودة
- `RequiresApproval` - يتطلب اعتماد

#### **PrisonerMovements (حركات النزلاء)**
- `Id` - المعرف الفريد
- `MovementType` - نوع الحركة
- `MovementCategory` - تصنيف الحركة
- `MovementDate` - تاريخ الحركة
- `ExpectedReturnDate` - تاريخ العودة المتوقع
- `ActualReturnDate` - تاريخ العودة الفعلي
- `Destination` - الوجهة
- `Reason` - السبب
- `Status` - حالة الحركة
- `FromWardId/ToWardId` - العنبر السابق/الجديد
- `FromRoomId/ToRoomId` - الغرفة السابقة/الجديدة
- `PrisonerId` - معرف السجين

### **4. جداول المستخدمين (ASP.NET Identity):**

#### **AspNetUsers (المستخدمين)**
- `Id` - المعرف الفريد
- `UserName` - اسم المستخدم
- `Email` - البريد الإلكتروني
- `PasswordHash` - كلمة المرور المشفرة
- `FullName` - الاسم الكامل
- `EmployeeNumber` - الرقم الوظيفي
- `Position` - المنصب
- `Department` - القسم
- `IsActive` - حالة النشاط

#### **AspNetRoles (الأدوار)**
- `Id` - المعرف الفريد
- `Name` - اسم الدور
- الأدوار: Admin, Manager, Officer, User

## 🔧 **كيفية الوصول لقاعدة البيانات:**

### **1. استخدام SQL Server Management Studio (SSMS):**
```
Server: (localdb)\mssqllocaldb
Authentication: Windows Authentication
Database: PrisonManagementDB
```

### **2. استخدام Visual Studio:**
1. View → SQL Server Object Explorer
2. Add SQL Server → (localdb)\mssqllocaldb
3. تصفح PrisonManagementDB

### **3. استخدام Azure Data Studio:**
```
Server: (localdb)\mssqllocaldb
Authentication: Windows Authentication
```

## 📋 **السكريبتات المتوفرة:**

### **1. CreateDatabase.sql**
- إنشاء قاعدة البيانات والجداول الأساسية
- إدراج البيانات الأولية (العنابر، أنواع الحركات)

### **2. CreateIdentityTables.sql**
- إنشاء جداول المستخدمين والأدوار
- إنشاء المستخدم الافتراضي (admin/Admin123!)

### **3. SampleData.sql**
- إدراج بيانات تجريبية (5 نزلاء، 6 قضايا، 3 حركات)
- إنشاء Views للاستعلامات المعقدة

## 🔍 **Views المتوفرة:**

### **vw_PrisonersDetails**
عرض شامل لبيانات النزلاء مع:
- البيانات الشخصية والصحية
- معلومات العنبر والغرفة
- حالة السجين (محبوس/مستحق الإفراج/موقوف)
- عدد القضايا وآخر تهمة

### **vw_WardsStatistics**
إحصائيات العنابر مع:
- نسبة الإشغال
- عدد الغرف
- حالة النشاط

## 🚀 **تشغيل السكريبتات:**

### **الطريقة الأولى: من خلال SSMS**
1. فتح SSMS والاتصال بـ (localdb)\mssqllocaldb
2. فتح ملف SQL
3. تنفيذ السكريبت (F5)

### **الطريقة الثانية: من خلال Command Line**
```bash
sqlcmd -S "(localdb)\mssqllocaldb" -i "CreateDatabase.sql"
sqlcmd -S "(localdb)\mssqllocaldb" -i "CreateIdentityTables.sql"
sqlcmd -S "(localdb)\mssqllocaldb" -i "SampleData.sql"
```

## 📊 **البيانات التجريبية المتوفرة:**

- **5 نزلاء** بجنسيات مختلفة (عراقي، سوري)
- **6 قضايا** متنوعة (جنايات، جنح)
- **3 حركات** (نقل داخلي، علاج خارجي، مأمورية)
- **3 عنابر** أساسية
- **6 غرف** موزعة على العنابر
- **6 شيل** موزعة على الغرف

## 🔐 **بيانات تسجيل الدخول:**
- **اسم المستخدم:** admin
- **كلمة المرور:** Admin123!
- **الدور:** Admin (صلاحيات كاملة)
