@model PrisonManagementSystem.Models.Ward

@{
    ViewData["Title"] = "إضافة عنبر جديد";
    Layout = "~/Views/Shared/_LayoutFilament.cshtml";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card military-card">
                <div class="card-header military-header">
                    <div class="header-content">
                        <i class="fas fa-building military-icon"></i>
                        <div class="header-text">
                            <h4>إضافة عنبر جديد</h4>
                            <div class="header-subtitle">ADD NEW WARD</div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form asp-action="Create">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="form-group">
                            <label asp-for="Name" class="control-label"></label>
                            <input asp-for="Name" class="form-control" placeholder="أدخل اسم العنبر" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="Description" class="control-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="3" placeholder="وصف العنبر (اختياري)"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="MaxCapacity" class="control-label"></label>
                            <input asp-for="MaxCapacity" class="form-control" type="number" min="1" placeholder="السعة القصوى للعنبر" />
                            <span asp-validation-for="MaxCapacity" class="text-danger"></span>
                            <small class="form-text text-muted">أدخل العدد الأقصى للنزلاء في هذا العنبر</small>
                        </div>

                        <div class="form-group">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-save"></i> حفظ العنبر
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <a asp-action="Index" class="btn btn-secondary btn-block">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // تأثيرات تفاعلية للنموذج
            $('.form-control').on('focus', function() {
                $(this).parent().addClass('focused');
            }).on('blur', function() {
                $(this).parent().removeClass('focused');
            });

            // تحقق من صحة البيانات مع تأثيرات بصرية
            $('form').on('submit', function(e) {
                var isValid = true;
                $('.form-control[required]').each(function() {
                    if (!$(this).val()) {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid').addClass('is-valid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    Swal.fire({
                        title: 'خطأ في البيانات',
                        text: 'يرجى ملء جميع الحقول المطلوبة',
                        icon: 'error',
                        confirmButtonText: 'حسناً',
                        confirmButtonColor: '#dc3545'
                    });
                }
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
}
