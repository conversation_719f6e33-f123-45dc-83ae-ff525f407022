/* Dashboard Specific Styles */

/* Enhanced Stats Cards */
.filament-stat-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.filament-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.filament-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
}

.filament-stat-card.success::before {
    background: linear-gradient(90deg, var(--success-500), #059669);
}

.filament-stat-card.warning::before {
    background: linear-gradient(90deg, var(--warning-500), #d97706);
}

.filament-stat-card.danger::before {
    background: linear-gradient(90deg, var(--danger-500), #dc2626);
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.chart-container canvas {
    max-height: 100%;
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
    padding-right: 20px;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    right: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gray-200);
}

.activity-item {
    position: relative;
    margin-bottom: 24px;
    padding-right: 40px;
}

.activity-item::before {
    content: '';
    position: absolute;
    right: 0;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: white;
    border: 3px solid var(--primary-500);
    z-index: 1;
}

.activity-item.success::before {
    border-color: var(--success-500);
}

.activity-item.warning::before {
    border-color: var(--warning-500);
}

.activity-item.danger::before {
    border-color: var(--danger-500);
}

/* Alert Enhancements */
.filament-alert {
    position: relative;
    overflow: hidden;
}

.filament-alert::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
}

.filament-alert.success::before {
    background: var(--success-500);
}

.filament-alert.warning::before {
    background: var(--warning-500);
}

.filament-alert.error::before {
    background: var(--danger-500);
}

.filament-alert.info::before {
    background: var(--primary-500);
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-top: 24px;
}

.quick-action-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    color: inherit;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
}

.quick-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    background: var(--primary-100);
    color: var(--primary-600);
}

.quick-action-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.quick-action-description {
    font-size: 14px;
    color: var(--gray-600);
}

/* Progress Bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill.success {
    background: linear-gradient(90deg, var(--success-500), #059669);
}

.progress-fill.warning {
    background: linear-gradient(90deg, var(--warning-500), #d97706);
}

.progress-fill.danger {
    background: linear-gradient(90deg, var(--danger-500), #dc2626);
}

/* Metric Cards */
.metric-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--gray-200);
    text-align: center;
}

.metric-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.metric-label {
    font-size: 14px;
    color: var(--gray-600);
    margin-bottom: 12px;
}

.metric-change {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.metric-change.positive {
    color: var(--success-500);
}

.metric-change.negative {
    color: var(--danger-500);
}

/* Loading States */
.dashboard-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--gray-500);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .filament-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .filament-stats {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .filament-stat-card {
        background: #1f2937;
        border-color: #374151;
    }
    
    .metric-card {
        background: #1f2937;
        border-color: #374151;
    }
    
    .quick-action-card {
        background: #1f2937;
        border-color: #374151;
    }
    
    .activity-timeline::before {
        background: #374151;
    }
}

/* Animation Keyframes */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-right: 4px solid var(--primary-500);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-right-color: var(--success-500);
}

.notification.warning {
    border-right-color: var(--warning-500);
}

.notification.error {
    border-right-color: var(--danger-500);
}
