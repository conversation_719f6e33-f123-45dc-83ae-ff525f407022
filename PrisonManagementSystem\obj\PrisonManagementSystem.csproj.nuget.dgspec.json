{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\code_z\\PrisonManagementSystem\\PrisonManagementSystem.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\code_z\\PrisonManagementSystem\\PrisonManagementSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\code_z\\PrisonManagementSystem\\PrisonManagementSystem.csproj", "projectName": "PrisonManagementSystem", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\code_z\\PrisonManagementSystem\\PrisonManagementSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\code_z\\PrisonManagementSystem\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[5.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}