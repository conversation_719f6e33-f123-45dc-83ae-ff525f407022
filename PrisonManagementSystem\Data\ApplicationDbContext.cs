using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using PrisonManagementSystem.Models;
using System;

namespace PrisonManagementSystem.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets للكيانات
        public DbSet<Prisoner> Prisoners { get; set; }
        public DbSet<Ward> Wards { get; set; }
        public DbSet<Room> Rooms { get; set; }
        public DbSet<Shila> Shilas { get; set; }
        public DbSet<PrisonerCase> PrisonerCases { get; set; }
        public DbSet<PrisonerMovement> PrisonerMovements { get; set; }
        public DbSet<MovementType> MovementTypes { get; set; }
        public DbSet<PrisonerPunishment> PrisonerPunishments { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // تكوين العلاقات
            builder.Entity<Prisoner>()
                .HasOne(p => p.Ward)
                .WithMany(w => w.Prisoners)
                .HasForeignKey(p => p.WardId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Entity<Prisoner>()
                .HasOne(p => p.Room)
                .WithMany(r => r.Prisoners)
                .HasForeignKey(p => p.RoomId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Entity<Room>()
                .HasOne(r => r.Ward)
                .WithMany(w => w.Rooms)
                .HasForeignKey(r => r.WardId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Entity<Shila>()
                .HasOne(s => s.Room)
                .WithMany(r => r.Shilas)
                .HasForeignKey(s => s.RoomId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Entity<PrisonerCase>()
                .HasOne(pc => pc.Prisoner)
                .WithMany(p => p.Cases)
                .HasForeignKey(pc => pc.PrisonerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<PrisonerMovement>()
                .HasOne(pm => pm.Prisoner)
                .WithMany(p => p.Movements)
                .HasForeignKey(pm => pm.PrisonerId)
                .OnDelete(DeleteBehavior.Cascade);

            // تكوين علاقات الحركة
            builder.Entity<PrisonerMovement>()
                .HasOne(pm => pm.FromWard)
                .WithMany()
                .HasForeignKey(pm => pm.FromWardId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Entity<PrisonerMovement>()
                .HasOne(pm => pm.FromRoom)
                .WithMany()
                .HasForeignKey(pm => pm.FromRoomId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Entity<PrisonerMovement>()
                .HasOne(pm => pm.ToWard)
                .WithMany()
                .HasForeignKey(pm => pm.ToWardId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Entity<PrisonerMovement>()
                .HasOne(pm => pm.ToRoom)
                .WithMany()
                .HasForeignKey(pm => pm.ToRoomId)
                .OnDelete(DeleteBehavior.NoAction);

            // تكوين علاقات العقوبات
            builder.Entity<PrisonerPunishment>()
                .HasOne(pp => pp.Prisoner)
                .WithMany(p => p.Punishments)
                .HasForeignKey(pp => pp.PrisonerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<PrisonerPunishment>()
                .HasOne(pp => pp.Shila)
                .WithMany(s => s.PrisonerPunishments)
                .HasForeignKey(pp => pp.ShilaId)
                .OnDelete(DeleteBehavior.NoAction);

            // تكوين علاقات العقوبات
            builder.Entity<PrisonerPunishment>()
                .HasOne(pp => pp.Prisoner)
                .WithMany(p => p.Punishments)
                .HasForeignKey(pp => pp.PrisonerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<PrisonerPunishment>()
                .HasOne(pp => pp.Shila)
                .WithMany(s => s.PrisonerPunishments)
                .HasForeignKey(pp => pp.ShilaId)
                .OnDelete(DeleteBehavior.NoAction);

            // تكوين الفهارس
            builder.Entity<Prisoner>()
                .HasIndex(p => p.PrisonerNumber)
                .IsUnique();

            builder.Entity<Prisoner>()
                .HasIndex(p => p.NationalNumber)
                .IsUnique();

            builder.Entity<PrisonerCase>()
                .HasIndex(pc => pc.CaseNumber);

            // إعداد البيانات الأولية
            SeedData(builder);
        }

        private void SeedData(ModelBuilder builder)
        {
            // إضافة أنواع الحركات الأساسية
            builder.Entity<MovementType>().HasData(
                new MovementType { Id = 1, Name = "إفراج", Description = "إفراج نهائي", Category = "خارجي", RequiresReturnDate = false, RequiresApproval = true, DisplayOrder = 1 },
                new MovementType { Id = 2, Name = "تشغيل خارجي", Description = "تشغيل خارج السجن", Category = "خارجي", RequiresReturnDate = true, RequiresApproval = true, DisplayOrder = 2 },
                new MovementType { Id = 3, Name = "مأمورية", Description = "مأمورية رسمية", Category = "خارجي", RequiresReturnDate = true, RequiresApproval = true, DisplayOrder = 3 },
                new MovementType { Id = 4, Name = "نقل داخلي", Description = "نقل بين العنابر والغرف", Category = "داخلي", RequiresReturnDate = false, RequiresApproval = false, DisplayOrder = 4 },
                new MovementType { Id = 5, Name = "علاج خارجي", Description = "علاج في مستشفى خارجي", Category = "خارجي", RequiresReturnDate = true, RequiresApproval = true, DisplayOrder = 5 }
            );

            // إضافة عنابر أساسية
            builder.Entity<Ward>().HasData(
                new Ward { Id = 1, Name = "العنبر الأول", Description = "عنبر الجنايات", MaxCapacity = 50, CurrentCount = 0 },
                new Ward { Id = 2, Name = "العنبر الثاني", Description = "عنبر الجنح", MaxCapacity = 40, CurrentCount = 0 },
                new Ward { Id = 3, Name = "العنبر الثالث", Description = "عنبر الموقوفين", MaxCapacity = 30, CurrentCount = 0 }
            );
        }
    }
}
