/* نظام الألوان العسكري */
:root {
    --military-primary: #2d5016;
    --military-secondary: #3d6b1f;
    --military-accent: #4a7c28;
    --military-light: #5a8c32;
    --military-dark: #1a3009;
    --military-camo-1: #3d5a27;
    --military-camo-2: #4a6b2f;
    --military-camo-3: #2d4419;
    --military-gold: #d4af37;
    --military-silver: #c0c0c0;
    --text-light: #f8f9fa;
    --text-dark: #2c3e50;
    --personal-color: #2980b9;
    --health-color: #27ae60;
    --prison-color: #f39c12;
}

/* خلفية عسكرية متحركة */
body {
    background: linear-gradient(135deg, var(--military-camo-1) 0%, var(--military-camo-2) 25%, var(--military-camo-3) 50%, var(--military-camo-1) 75%, var(--military-camo-2) 100%) !important;
    background-size: 400% 400% !important;
    animation: militaryGradient 15s ease infinite !important;
    min-height: 100vh !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

@keyframes militaryGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* صفحة تسجيل الدخول */
.login-container {
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: linear-gradient(135deg, var(--military-camo-1) 0%, var(--military-camo-2) 25%, var(--military-camo-3) 50%, var(--military-camo-1) 75%, var(--military-camo-2) 100%) !important;
    background-size: 400% 400% !important;
    animation: militaryGradient 15s ease infinite !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    position: relative !important;
    overflow: hidden !important;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.login-card {
    background: rgba(255, 255, 255, 0.95) !important;
    padding: 3rem 2.5rem !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3) !important;
    width: 100% !important;
    max-width: 450px !important;
    text-align: center !important;
    border: 3px solid var(--military-gold) !important;
    backdrop-filter: blur(15px) !important;
    position: relative !important;
    overflow: hidden !important;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--military-primary), var(--military-gold), var(--military-accent));
}

.login-header {
    margin-bottom: 2.5rem;
    position: relative;
}

.military-emblem {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--military-primary), var(--military-accent));
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid var(--military-gold);
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.3);
}

.military-emblem i {
    font-size: 2.5rem;
    color: var(--military-gold);
}

.login-header h2 {
    color: var(--military-primary);
    margin-bottom: 0.5rem;
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.login-header p {
    color: var(--military-secondary);
    margin-bottom: 0;
    font-size: 1.1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.military-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--military-gold);
    color: var(--military-secondary);
    font-size: 0.9rem;
}

/* الصفحة الرئيسية */
.dashboard-container {
    padding: 2rem 0;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
    background: rgba(45, 80, 22, 0.9);
    padding: 3rem 2rem;
    border-radius: 20px;
    border: 3px solid var(--military-gold);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shine 4s infinite;
}

.header-emblem {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--military-gold) 0%, #f4d03f 100%);
    border-radius: 50%;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 5px solid white;
    box-shadow: 0 10px 35px rgba(212, 175, 55, 0.5);
    animation: pulse 2s infinite;
    position: relative;
    z-index: 2;
}

.header-emblem i {
    font-size: 3.5rem;
    color: var(--military-dark);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.dashboard-header h1 {
    color: var(--military-gold);
    margin-bottom: 1rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    font-size: 2.8rem;
    position: relative;
    z-index: 2;
}

.header-subtitle {
    font-size: 1.2rem;
    color: var(--military-silver);
    letter-spacing: 4px;
    margin-bottom: 2rem;
    text-transform: uppercase;
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.welcome-section {
    margin-top: 2rem;
    padding: 2rem;
    background: rgba(255,255,255,0.15);
    border-radius: 20px;
    border: 2px solid var(--military-gold);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
}

.user-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--military-gold), #f4d03f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--military-dark);
    font-size: 2rem;
    border: 4px solid white;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.user-details {
    text-align: right;
}

.user-name {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.user-role {
    display: block;
    font-size: 1.1rem;
    color: var(--military-gold);
    margin-bottom: 0.3rem;
    font-weight: 600;
}

.login-time {
    display: block;
    font-size: 1rem;
    color: var(--military-silver);
}

/* تصميم بطاقات القائمة المحسن */
.dashboard-menu {
    margin-bottom: 4rem;
}

.menu-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.4s ease;
    border: 3px solid transparent;
    backdrop-filter: blur(15px);
    position: relative;
    height: 100%;
    min-height: 320px;
}

.menu-card:hover {
    transform: translateY(-10px) scale(1.03);
    border-color: var(--military-gold);
    box-shadow: 0 25px 60px rgba(212, 175, 55, 0.3);
}

.menu-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.menu-card:hover::before {
    opacity: 1;
}

.menu-header {
    background: linear-gradient(135deg, var(--military-primary) 0%, var(--military-secondary) 50%, var(--military-accent) 100%);
    color: white;
    padding: 2.5rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.menu-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.menu-card:hover .menu-header::after {
    left: 100%;
}

.menu-header i {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: var(--military-gold);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    display: block;
    animation: float 3s ease-in-out infinite;
}

.menu-header h3 {
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    margin: 0;
    font-size: 1.4rem;
    letter-spacing: 1px;
}

.menu-items {
    padding: 2rem;
    background: rgba(255,255,255,0.9);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 1.2rem 1.5rem;
    color: var(--military-dark);
    text-decoration: none;
    border-radius: 15px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    background: rgba(255,255,255,0.7);
    backdrop-filter: blur(5px);
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--military-light), var(--military-accent));
    transition: left 0.3s ease;
    z-index: -1;
}

.menu-item:hover {
    color: white;
    text-decoration: none;
    border-color: var(--military-gold);
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.3);
}

.menu-item:hover::before {
    left: 0;
}

.menu-item i {
    margin-left: 1rem;
    width: 30px;
    color: var(--military-accent);
    transition: all 0.3s ease;
    font-size: 1.3rem;
}

.menu-item:hover i {
    color: var(--military-gold);
    transform: scale(1.2);
}

/* البطاقات العسكرية */
.military-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    overflow: hidden;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border: 2px solid var(--military-gold);
    transition: all 0.3s ease;
}

.military-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 45px rgba(0,0,0,0.3);
    border-color: var(--military-gold);
}

/* رؤوس البطاقات العسكرية */
.military-header {
    background: linear-gradient(135deg, var(--military-primary) 0%, var(--military-secondary) 50%, var(--military-accent) 100%);
    color: white;
    padding: 1.5rem 2rem;
    border-bottom: 3px solid var(--military-gold);
    position: relative;
    overflow: hidden;
}

.military-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shine 4s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* الأيقونات العسكرية */
.military-icon {
    color: var(--military-gold);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* الأزرار العسكرية */
.military-btn {
    background: var(--military-gold);
    color: var(--military-dark);
    border: 2px solid white;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.military-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.military-btn:hover {
    background: white;
    color: var(--military-primary);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    text-decoration: none;
}

.military-btn:hover::before {
    left: 100%;
}

/* الجداول العسكرية */
.military-table {
    background: rgba(255,255,255,0.95);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.military-table .thead-dark {
    background: linear-gradient(135deg, var(--military-primary), var(--military-secondary));
    color: var(--military-gold);
}

.military-table .thead-dark th {
    border: none;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    padding: 1.2rem 1rem;
}

.military-table tbody tr:nth-of-type(odd) {
    background-color: rgba(45, 80, 22, 0.05);
}

.military-table tbody tr:hover {
    background-color: rgba(212, 175, 55, 0.1);
    transform: scale(1.01);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* الشارات العسكرية */
.military-badge {
    background: var(--military-gold);
    color: var(--military-dark);
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

/* النماذج العسكرية */
.military-form .form-control {
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 1rem 1.2rem;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.9);
}

.military-form .form-control:focus {
    border-color: var(--military-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
    background: white;
    transform: translateY(-2px);
}

/* أشرطة التقدم العسكرية */
.military-progress {
    height: 25px;
    border-radius: 15px;
    background: rgba(45, 80, 22, 0.1);
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.military-progress .progress-bar {
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9rem;
    background: linear-gradient(90deg, var(--military-light), var(--military-accent));
    transition: all 0.3s ease;
}

/* القوائم المنسدلة العسكرية */
.military-dropdown {
    border: 2px solid var(--military-gold);
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    background: var(--military-primary);
}

.military-dropdown .dropdown-item {
    color: var(--text-light);
    transition: all 0.3s ease;
    padding: 0.8rem 1.5rem;
}

.military-dropdown .dropdown-item:hover {
    background: var(--military-gold);
    color: var(--military-dark);
}

/* تأثيرات الحركة */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-row {
    animation: fadeInRow 0.5s ease-out;
    animation-fill-mode: both;
}

@keyframes fadeInRow {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تأثيرات الإضاءة */
.glow-effect {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

/* تصميم بطاقات الإحصائيات المحسن */
.dashboard-stats {
    margin-top: 3rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    transition: all 0.4s ease;
    border: 3px solid transparent;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    min-height: 140px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s ease;
    opacity: 0;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: var(--military-gold);
    box-shadow: 0 25px 60px rgba(212, 175, 55, 0.3);
}

.stat-card:hover::before {
    opacity: 1;
    animation: shine 1s ease-in-out;
}

.stat-icon {
    background: linear-gradient(135deg, var(--military-primary) 0%, var(--military-accent) 100%);
    color: var(--military-gold);
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 2rem;
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.4);
    border: 4px solid var(--military-gold);
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.stat-card:hover .stat-icon::before {
    left: 100%;
}

.stat-icon i {
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: float 3s ease-in-out infinite;
}

.stat-info {
    flex-grow: 1;
    text-align: right;
}

.stat-info h4 {
    margin: 0 0 0.5rem 0;
    color: var(--military-dark);
    font-size: 1.3rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    color: var(--military-primary);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    line-height: 1;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--military-primary), var(--military-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-subtitle {
    font-size: 0.9rem;
    color: var(--military-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
    to { box-shadow: 0 0 30px rgba(212, 175, 55, 0.8); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* تأثيرات إضافية */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تطبيق التأثيرات */
.dashboard-container {
    animation: fadeInUp 0.8s ease-out;
}

.menu-card {
    animation: slideInRight 0.6s ease-out;
    animation-fill-mode: both;
}

.menu-card:nth-child(1) { animation-delay: 0.1s; }
.menu-card:nth-child(2) { animation-delay: 0.2s; }
.menu-card:nth-child(3) { animation-delay: 0.3s; }
.menu-card:nth-child(4) { animation-delay: 0.4s; }

.stat-card {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.5s; }
.stat-card:nth-child(2) { animation-delay: 0.6s; }
.stat-card:nth-child(3) { animation-delay: 0.7s; }
.stat-card:nth-child(4) { animation-delay: 0.8s; }

/* شريط التنقل العسكري */
.military-navbar {
    background: linear-gradient(135deg, var(--military-dark) 0%, var(--military-primary) 50%, var(--military-secondary) 100%);
    border-bottom: 3px solid var(--military-gold);
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    padding: 0.5rem 0;
    backdrop-filter: blur(10px);
}

.military-brand {
    display: flex;
    align-items: center;
    color: var(--military-gold) !important;
    text-decoration: none;
    font-weight: bold;
    padding: 0.5rem 0;
}

.military-icon {
    font-size: 2.5rem;
    margin-left: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.brand-text {
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.brand-subtitle {
    font-size: 0.8rem;
    color: var(--military-silver);
    font-weight: normal;
    margin-top: -0.2rem;
    letter-spacing: 2px;
}

.military-nav {
    margin-right: 2rem;
}

.military-nav-link {
    color: var(--text-light) !important;
    padding: 0.8rem 1.5rem !important;
    margin: 0 0.2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.military-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--military-gold), var(--military-light));
    transition: left 0.3s ease;
    z-index: -1;
}

.military-nav-link:hover {
    color: var(--military-dark) !important;
    transform: translateY(-2px);
}

.military-nav-link:hover::before {
    left: 0;
}

.military-nav-link i {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

.military-user-menu {
    color: var(--text-light) !important;
    padding: 0.5rem 1rem !important;
    border: 2px solid var(--military-gold);
    border-radius: 10px;
    background: rgba(45, 80, 22, 0.3);
    transition: all 0.3s ease;
}

.military-user-menu:hover {
    background: var(--military-gold);
    color: var(--military-dark) !important;
}

.user-avatar {
    display: inline-block;
    width: 35px;
    height: 35px;
    background: var(--military-gold);
    border-radius: 50%;
    text-align: center;
    line-height: 35px;
    color: var(--military-dark);
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

.user-name {
    font-weight: bold;
    margin-right: 0.5rem;
}

.user-rank {
    font-size: 0.8rem;
    color: var(--military-silver);
    display: block;
    margin-top: -0.2rem;
}

.logout-btn {
    border: none;
    background: none;
    width: 100%;
    text-align: right;
}

/* إضافة أنماط إضافية لضمان التطبيق */
html, body {
    background: linear-gradient(135deg, var(--military-camo-1) 0%, var(--military-camo-2) 25%, var(--military-camo-3) 50%, var(--military-camo-1) 75%, var(--military-camo-2) 100%) !important;
    background-size: 400% 400% !important;
    animation: militaryGradient 15s ease infinite !important;
    min-height: 100vh !important;
}

/* أنماط إضافية لصفحة تسجيل الدخول */
.form-control {
    border: 2px solid #e0e0e0 !important;
    border-radius: 10px !important;
    padding: 1rem 1.2rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: rgba(255,255,255,0.9) !important;
    text-align: right !important;
    direction: rtl !important;
}

.form-control:focus {
    outline: none !important;
    border-color: var(--military-gold) !important;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2) !important;
    background: white !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--military-primary), var(--military-accent)) !important;
    border: 2px solid var(--military-gold) !important;
    color: white !important;
    padding: 1rem !important;
    border-radius: 10px !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    width: 100% !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--military-accent), var(--military-light)) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.4) !important;
}

.form-group {
    margin-bottom: 1.5rem !important;
    text-align: right !important;
}

.form-label, .control-label {
    display: block !important;
    margin-bottom: 0.8rem !important;
    color: var(--military-primary) !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

/* تخصيصات الاستجابة */
@media (max-width: 768px) {
    .military-card {
        margin: 1rem;
        border-radius: 10px;
    }
    
    .military-header {
        padding: 1rem;
    }
    
    .military-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}
