# PowerShell script to update all view files to use Filament layout

$viewFiles = @(
    "Views/Home/Privacy.cshtml",
    "Views/Prisoners/Details.cshtml",
    "Views/Prisoners/Edit.cshtml",
    "Views/Prisoners/Delete.cshtml",
    "Views/Wards/Details.cshtml",
    "Views/Wards/Edit.cshtml",
    "Views/Wards/Delete.cshtml",
    "Views/Rooms/Index.cshtml",
    "Views/Rooms/Create.cshtml",
    "Views/Rooms/Details.cshtml",
    "Views/Rooms/Edit.cshtml",
    "Views/Rooms/Delete.cshtml",
    "Views/PrisonerCases/Index.cshtml",
    "Views/PrisonerCases/Create.cshtml",
    "Views/PrisonerCases/Details.cshtml",
    "Views/PrisonerCases/Edit.cshtml",
    "Views/PrisonerCases/Delete.cshtml",
    "Views/PrisonerMovements/Index.cshtml",
    "Views/PrisonerMovements/Create.cshtml",
    "Views/PrisonerMovements/Details.cshtml",
    "Views/PrisonerMovements/Edit.cshtml",
    "Views/PrisonerMovements/Delete.cshtml"
)

foreach ($file in $viewFiles) {
    if (Test-Path $file) {
        Write-Host "Updating $file"
        $content = Get-Content $file -Raw
        
        # Update layout reference
        if ($content -match '@\{\s*ViewData\["Title"\]\s*=\s*"([^"]+)";\s*\}') {
            $title = $matches[1]
            $newContent = $content -replace '@\{\s*ViewData\["Title"\]\s*=\s*"[^"]+";?\s*\}', "@{`n    ViewData[`"Title`"] = `"$title`";`n    Layout = `"~/Views/Shared/_LayoutFilament.cshtml`";`n}"
            Set-Content -Path $file -Value $newContent -Encoding UTF8
        }
        elseif ($content -match '@\{\s*ViewData\["Title"\]\s*=\s*"([^"]+)";\s*Layout\s*=\s*"[^"]+";?\s*\}') {
            $title = $matches[1]
            $newContent = $content -replace '@\{\s*ViewData\["Title"\]\s*=\s*"[^"]+";?\s*Layout\s*=\s*"[^"]+";?\s*\}', "@{`n    ViewData[`"Title`"] = `"$title`";`n    Layout = `"~/Views/Shared/_LayoutFilament.cshtml`";`n}"
            Set-Content -Path $file -Value $newContent -Encoding UTF8
        }
    }
    else {
        Write-Host "File not found: $file"
    }
}

Write-Host "Layout update completed!"
